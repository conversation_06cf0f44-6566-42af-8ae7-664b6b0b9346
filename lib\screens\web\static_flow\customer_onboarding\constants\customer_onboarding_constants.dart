import '../../../../../models/customer_model.dart';

/// Constants for the customer onboarding process
class CustomerOnboardingConstants {
  /// Process steps configuration
  static const List<Map<String, dynamic>> processSteps = [
    {
      'title': 'Core Metadata',
      'status': '',
      'count': '',
      'showAttributeTable': true,
    },
    {
      'title': 'Process Ownership',
      'status': '',
      'count': '',
      'showAttributeTable': true,
    },
    {
      'title': 'Trigger Definition',
      'status': '',
      'count': '',
      'showAttributeTable': false,
    },
    {
      'title': 'Local Objectives',
      'status': '',
      'count': '',
      'showAttributeTable': false,
    },
    {
      'title': 'Pathways Definitions',
      'status': '',
      'count': '',
      'showAttributeTable': false,
    },
    {
      'title': 'Pathways Detail',
      'status': '',
      'count': '',
      'showAttributeTable': false,
    },
    {
      'title': 'Business Rules',
      'status': '',
      'count': '',
      'showAttributeTable': false,
    },
    {
      'title': 'Validation Rules',
      'status': '',
      'count': '',
      'showAttributeTable': false,
    },
  ];

  /// Field options for dropdowns
  static const List<String> fieldOptions = [
    'name',
    'version',
    'status',
    'agent_type',
    'description',
    'primary_entity',
    'classification',
    'priority',
    'category',
    'owner',
  ];

  /// Value options for dropdowns
  static const List<String> valueOptions = [
    'CustomerOnboarding',
    'ProcessAnalysis',
    'DataValidation',
    'RuleConfiguration',
    'PathwayDefinition',
  ];

  /// Status options
  static const List<String> statusOptions = [
    'Active',
    'Inactive',
    'Pending',
    'Completed',
    'Draft',
    'Archived',
  ];

  /// Agent type options
  static const List<String> agentTypeOptions = [
    'SYSTEM',
    'Human',
    'Machine',
    'Hybrid',
    'AI',
  ];

  /// Default business rules data
  static List<BusinessRule1> get defaultBusinessRules => [
        BusinessRule1(
          field: 'Name',
          value: 'Customer/Onboarding',
          source: 'Extracted',
          sourceColor: 'green',
        ),
        BusinessRule1(
          field: 'Display Name',
          value: 'Customer Onboarding Process',
          source: 'Auto-generated',
          sourceColor: 'blue',
        ),
        BusinessRule1(
          field: 'Description',
          value:
              'Comprehensive customer onboarding process for business logic analysis',
          source: 'Generated',
          sourceColor: 'orange',
        ),
        BusinessRule1(
          field: 'Primary Entity',
          value: 'Customer_Entity',
          source: 'Inferred',
          sourceColor: 'orange',
        ),
        BusinessRule1(
          field: 'Book',
          value: 'Customer Management',
          source: 'Extracted',
          sourceColor: 'green',
        ),
        BusinessRule1(
          field: 'Chapter',
          value: 'Customer Lifecycle',
          source: 'Generated',
          sourceColor: 'orange',
        ),
      ];

  /// Sample object data for different types
  static const Map<String, List<Map<String, dynamic>>> sampleObjectData = {
    'customer': [
      {
        'field': 'Customer Name',
        'value': 'Business Process Automation',
        'type': 'String',
        'required': 'Yes',
      },
      {
        'field': 'Customer Type',
        'value': 'Enterprise',
        'type': 'Selection',
        'required': 'Yes',
      },
      {
        'field': 'Priority Level',
        'value': 'High',
        'type': 'Selection',
        'required': 'Yes',
      },
      {
        'field': 'Onboarding Date',
        'value': '2024-01-15',
        'type': 'Date',
        'required': 'No',
      },
    ],
    'process': [
      {
        'field': 'Process Name',
        'value': 'Data Validation',
        'type': 'String',
        'required': 'Yes',
      },
      {
        'field': 'Process Owner',
        'value': 'System Administrator',
        'type': 'String',
        'required': 'Yes',
      },
      {
        'field': 'Execution Mode',
        'value': 'Automated',
        'type': 'Selection',
        'required': 'Yes',
      },
      {
        'field': 'Frequency',
        'value': 'Daily',
        'type': 'Selection',
        'required': 'No',
      },
    ],
    'validation': [
      {
        'field': 'Rule Name',
        'value': 'Data Completeness Check',
        'type': 'String',
        'required': 'Yes',
      },
      {
        'field': 'Validation Type',
        'value': 'Pre-deployment',
        'type': 'Selection',
        'required': 'Yes',
      },
      {
        'field': 'Error Handling',
        'value': 'Stop on Error',
        'type': 'Selection',
        'required': 'Yes',
      },
      {
        'field': 'Notification Level',
        'value': 'Critical',
        'type': 'Selection',
        'required': 'No',
      },
    ],
  };
}
