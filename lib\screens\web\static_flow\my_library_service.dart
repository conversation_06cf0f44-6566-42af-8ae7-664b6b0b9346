import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart' hide Summary;
import 'package:nsl/config/environment.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/services/dio_client.dart';
import 'package:nsl/utils/logger.dart';
import 'get_all_my_library_model.dart';
import '../../../services/auth_service.dart';

class MyLibraryService {
  final Dio _dio = DioClient().client;

  // Base URL
  late final String _baseUrl;
  late final String _libraryUrl;

  // Singleton instance
  static final MyLibraryService _instance = MyLibraryService._internal();

  // Factory constructor
  factory MyLibraryService() => _instance;

  final _authService = AuthService();

  // Internal constructor
  MyLibraryService._internal() {
    // Initialize base URL from environment
    _baseUrl = Environment.validateBaseUrl;
    _libraryUrl = '$_baseUrl/library';
  }

  // Cache for library data and processed attributes
  static Library? _cachedLibraryData;
  static List<String>? _cachedPublishedAttributeNames;
  static List<String>? _cachedAllAttributeNames;
  static bool _isInitialized = false;

  // Helper method to parse JSON response
  static GetAllMylibrarylist? _parseGetAllMylibrarylist(
      Map<String, dynamic> json) {
    try {
      return GetAllMylibrarylist(
        success: json['success'],
        message: json['message'],
        data: json['data'] != null ? _parseData(json['data']) : null,
        error: json['error'],
        timestamp: json['timestamp'] != null
            ? DateTime.tryParse(json['timestamp'].toString())
            : null,
      );
    } catch (e) {
      debugPrint('Error parsing GetAllMylibrarylist: $e');
      return null;
    }
  }

  // Helper method to parse Data
  static Data? _parseData(Map<String, dynamic> json) {
    try {
      return Data(
        tenantId: json['tenant_id'],
        dataLibrary:
            json['library'] != null ? _parseLibrary(json['library']) : null,
        summary:
            json['summary'] != null ? _parseSummary(json['summary']) : null,
        metadata:
            json['metadata'] != null ? _parseMetadata(json['metadata']) : null,
      );
    } catch (e) {
      debugPrint('Error parsing Data: $e');
      return null;
    }
  }

  // Helper method to parse Library
  static Library? _parseLibrary(Map<String, dynamic> json) {
    try {
      return Library(
        roles: json['roles'] != null ? _parseRoles(json['roles']) : null,
        entities:
            json['entities'] != null ? _parseEntities(json['entities']) : null,
        entityAttributes: json['entity_attributes'] != null
            ? _parseEntityAttributes(json['entity_attributes'])
            : null,
        departments: json['departments'] != null
            ? _parseDepartments(json['departments'])
            : null,
        businessRules: json['business_rules'] != null
            ? _parseBusinessRules(json['business_rules'])
            : null,
        globalObjectives: json['global_objectives'] != null
            ? _parseGlobalObjectives(json['global_objectives'])
            : null,
        localObjectives: json['local_objectives'] != null
            ? _parseLocalObjectives(json['local_objectives'])
            : null,
        systemPermissions: json['system_permissions'] != null
            ? _parseSystemPermissions(json['system_permissions'])
            : null,
        securityProperties: json['security_properties'] != null
            ? _parseSecurityProperties(json['security_properties'])
            : null,
        entityRelationships: json['entity_relationships'] != null
            ? _parseEntityRelationships(json['entity_relationships'])
            : null,
        attributeValidations: json['attribute_validations'] != null
            ? _parseAttributeValidations(json['attribute_validations'])
            : null,
      );
    } catch (e) {
      debugPrint('Error parsing Library: $e');
      return null;
    }
  }

  static SystemPermissions? _parseSystemPermissions(
          Map<String, dynamic> json) =>
      null;
  static SecurityProperties? _parseSecurityProperties(
          Map<String, dynamic> json) =>
      null;
  static EntityRelationships? _parseEntityRelationships(
          Map<String, dynamic> json) =>
      null;
  static AttributeValidations? _parseAttributeValidations(
          Map<String, dynamic> json) =>
      null;

  // Helper method to parse Roles
  static Roles? _parseRoles(Map<String, dynamic> json) {
    try {
      return Roles(
        postgres: json['postgres'] != null
            ? (json['postgres'] as List)
                .map((e) => _parseRolesPostgre(e))
                .where((e) => e != null)
                .cast<RolesPostgre>()
                .toList()
            : null,
        mongoDrafts: json['mongo_drafts'] != null
            ? (json['mongo_drafts'] as List)
                .map((e) => _parseRolesPostgre(e))
                .where((e) => e != null)
                .cast<RolesPostgre>()
                .toList()
            : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing Roles: $e');
      return null;
    }
  }

  static RolesPostgre? _parseRolesPostgre(Map<String, dynamic> json) {
    try {
      return RolesPostgre(
        roleId: json['role_id'],
        name: json['name'],
        tenantId: json['tenant_id'],
        description: json['description'],
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString())
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString())
            : null,
        version: json['version'],
        naturalLanguage: json['natural_language'],
      );
    } catch (e) {
      debugPrint('Error parsing RolesPostgre: $e');
      return null;
    }
  }

  // Helper method to parse Entities
  static Entities? _parseEntities(Map<String, dynamic> json) {
    try {
      return Entities(
        postgres: json['postgres'] != null
            ? (json['postgres'] as List)
                .map((e) => _parseEntitiesMongoDraft(e))
                .where((e) => e != null)
                .cast<EntitiesMongoDraft>()
                .toList()
            : null,
        mongoDrafts: json['mongo_drafts'] != null
            ? (json['mongo_drafts'] as List)
                .map((e) => _parseEntitiesMongoDraft(e))
                .where((e) => e != null)
                .cast<EntitiesMongoDraft>()
                .toList()
            : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing Entities: $e');
      return null;
    }
  }

  static EntitiesMongoDraft? _parseEntitiesMongoDraft(
      Map<String, dynamic> json) {
    try {
      return EntitiesMongoDraft(
        id: json['id'] ?? json['_id'], // Handle both id and _id fields
        entityId: json['entity_id'],
        name: json['name'],
        displayName: json['display_name'],
        tenantId: json['tenant_id'],
        tenantName: json['tenant_name'],
        description: json['description'],
        tableName: json['table_name'],
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString())
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString())
            : null,
        createdBy: json['created_by'],
        updatedBy: json['updated_by'],
        version: json['version'],
        status: json['status'],
        type: json['type'],
        naturalLanguage: json['natural_language'],
        businessDomain: json['business_domain'],
        category: json['category'],
        tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
        archivalStrategy: json['archival_strategy'],
        icon: json['icon'],
        colourTheme: json['colour_theme'],
        entityStatus: json['entity_status'],
        changesDetected: json['changes_detected'] != null
            ? List<dynamic>.from(json['changes_detected'])
            : null,
        iconType: json['icon_type'],
        iconContent: json['icon_content'],
      );
    } catch (e) {
      debugPrint('Error parsing EntitiesMongoDraft: $e');
      return null;
    }
  }

  // Helper method to parse EntityAttributes
  static EntityAttributes? _parseEntityAttributes(Map<String, dynamic> json) {
    try {
      return EntityAttributes(
        postgres: json['postgres'] != null
            ? (json['postgres'] as List)
                .map((e) => _parseEntityAttributesMongoDraft(e))
                .where((e) => e != null)
                .cast<EntityAttributesMongoDraft>()
                .toList()
            : null,
        mongoDrafts: json['mongo_drafts'] != null
            ? (json['mongo_drafts'] as List)
                .map((e) => _parseEntityAttributesMongoDraft(e))
                .where((e) => e != null)
                .cast<EntityAttributesMongoDraft>()
                .toList()
            : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing EntityAttributes: $e');
      return null;
    }
  }

  static EntityAttributesMongoDraft? _parseEntityAttributesMongoDraft(
      Map<String, dynamic> json) {
    try {
      return EntityAttributesMongoDraft(
        id: json['id'] ?? json['_id'], // Handle both id and _id fields
        attributeId: json['attribute_id'],
        entityId: json['entity_id'],
        name: json['name'],
        displayName: json['display_name'],
        description: json['description'],
        datatype: json['datatype'],
        isPrimaryKey: json['is_primary_key'],
        isForeignKey: json['is_foreign_key'],
        isRequired: json['is_required'],
        isUnique: json['is_unique'],
        defaultType: json['default_type'],
        defaultValue: json['default_value'],
        helperText: json['helper_text'],
        isCalculated: json['is_calculated'],
        calculationFormula: json['calculation_formula'],
        status: json['status'],
        required: json['required'],
        calculatedField: json['calculated_field'],
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString())
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString())
            : null,
        createdBy: json['created_by'],
        updatedBy: json['updated_by'],
        version: json['version'],
        naturalLanguage: json['natural_language'],
        tenantId: json['tenant_id'],
      );
    } catch (e) {
      debugPrint('Error parsing EntityAttributesMongoDraft: $e');
      return null;
    }
  }

  // Placeholder methods for other parsing functions
  static Departments? _parseDepartments(Map<String, dynamic> json) {
    try {
      List<DepartmentsPostgre>? postgresRecords;
      List<PostgresRecordElement>? mongoDraftRecords;

      // Parse postgres records
      if (json['postgres'] != null) {
        try {
          postgresRecords = (json['postgres'] as List)
              .map((e) => _parseDepartmentsPostgre(e))
              .where((e) => e != null)
              .cast<DepartmentsPostgre>()
              .toList();
        } catch (e) {
          debugPrint('Error parsing postgres departments: $e');
        }
      }

      // Parse mongo draft records
      if (json['mongo_drafts'] != null) {
        try {
          mongoDraftRecords = (json['mongo_drafts'] as List)
              .map((e) => _parsePostgresRecordElement(e))
              .where((e) => e != null)
              .cast<PostgresRecordElement>()
              .toList();
        } catch (e) {
          debugPrint('Error parsing mongo_drafts departments: $e');
          debugPrint('mongo_drafts data: ${json['mongo_drafts']}');
        }
      }

      return Departments(
        postgres: postgresRecords,
        mongoDrafts: mongoDraftRecords,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e, stackTrace) {
      debugPrint('Error parsing Departments: $e');
      debugPrint('Stack trace: $stackTrace');
      debugPrint('JSON keys: ${json.keys.toList()}');
      return null;
    }
  }

  static DepartmentsPostgre? _parseDepartmentsPostgre(
      Map<String, dynamic> json) {
    try {
      return DepartmentsPostgre(
        departmentId: json['department_id'],
        name: json['name'],
        description: json['description'],
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString())
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString())
            : null,
        tenantId: json['tenant_id'],
        naturalLanguage: json['natural_language'],
        version: json['version'],
      );
    } catch (e) {
      debugPrint('Error parsing DepartmentsPostgre: $e');
      return null;
    }
  }

  /// Parses a PostgresRecordElement from JSON data
  ///
  /// This method has been enhanced to handle all fields present in the PostgresRecordElement class:
  /// - Added missing fields: mongoDraftId, departmentHeadRoleId, parentDepartmentId, createdBy, updatedBy, status
  /// - Added proper enum parsing for createdBy and updatedBy fields
  /// - Added robust error handling and data validation
  /// - Added helper functions for safe type conversion
  static PostgresRecordElement? _parsePostgresRecordElement(
      Map<String, dynamic> json) {
    try {
      // Validate that we have a valid JSON object
      if (json.isEmpty) {
        debugPrint(
            'Warning: Empty JSON object provided to _parsePostgresRecordElement');
        return null;
      }

      // Helper function to safely parse integers
      int? safeParseInt(dynamic value) {
        if (value == null) return null;
        if (value is int) return value;
        if (value is String) return int.tryParse(value);
        return int.tryParse(value.toString());
      }

      // Helper function to safely parse DateTime
      DateTime? safeParseDateTime(dynamic value) {
        if (value == null) return null;
        try {
          return DateTime.tryParse(value.toString());
        } catch (e) {
          debugPrint('Warning: Failed to parse DateTime from value: $value');
          return null;
        }
      }

      return PostgresRecordElement(
        id: json['id']?.toString(),
        mongoDraftId: safeParseInt(json['mongo_draft_id']),
        tenantId: json['tenant_id'],
        name: json['name']?.toString(),
        description: json['description']?.toString(),
        departmentHeadRoleId:
            json['department_head_role_id'], // Can be dynamic type
        parentDepartmentId: safeParseInt(json['parent_department_id']),
        naturalLanguage: json['natural_language']?.toString(),
        createdAt: safeParseDateTime(json['created_at']),
        createdBy: json['created_by'],
        updatedAt: safeParseDateTime(json['updated_at']),
        updatedBy: json['updated_by'],
        version: safeParseInt(json['version']),
        status: json['status']?.toString(),
      );
    } catch (e, stackTrace) {
      debugPrint('Error parsing PostgresRecordElement: $e');
      debugPrint('Stack trace: $stackTrace');
      debugPrint('JSON data keys: ${json.keys.toList()}');
      debugPrint('JSON data: $json');
      return null;
    }
  }

  static BusinessRules? _parseBusinessRules(Map<String, dynamic> json) => null;

  static GlobalObjectives? _parseGlobalObjectives(Map<String, dynamic> json) {
    try {
      return GlobalObjectives(
        postgres: json['postgres'] != null
            ? (json['postgres'] as List)
                .map((e) => _parseGlobalObjectivesPostgre(e))
                .where((e) => e != null)
                .cast<GlobalObjectivesPostgre>()
                .toList()
            : null,
        mongoDrafts: json['mongo_drafts'] as List?,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing GlobalObjectives: $e');
      return null;
    }
  }

  static GlobalObjectivesPostgre? _parseGlobalObjectivesPostgre(
      Map<String, dynamic> json) {
    try {
      return GlobalObjectivesPostgre(
          goId: json['go_id'],
          name: json['name'],
          description: json['description'],
          tenantId: json['tenant_id'],
          createdAt: json['created_at'] != null
              ? DateTime.tryParse(json['created_at'].toString())
              : null,
          updatedAt: json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'].toString())
              : null,
          version: json['version'],
          naturalLanguage: json['natural_language'],
          primaryEntity: json['primary_entity'],
          classification: json['classification'],
          bookId: json['book_id'],
          bookName: json['book_name'],
          chapterId: json['chapter_id'],
          chapterName: json['chapter_name'],
          status: json['status']);
    } catch (e) {
      debugPrint('Error parsing GlobalObjectivesPostgre: $e');
      return null;
    }
  }

  static LocalObjectives? _parseLocalObjectives(Map<String, dynamic> json) {
    try {
      return LocalObjectives(
        postgres: json['postgres'] != null
            ? (json['postgres'] as List)
                .map((e) => _parseLocalObjectivesPostgre(e))
                .where((e) => e != null)
                .cast<LocalObjectivesPostgre>()
                .toList()
            : null,
        mongoDrafts: json['mongo_drafts'] != null
            ? (json['mongo_drafts'] as List)
                .map((e) => _parseLocalObjectivesPostgre(e))
                .where((e) => e != null)
                .cast<LocalObjectivesPostgre>()
                .toList()
            : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing LocalObjectives: $e');
      return null;
    }
  }

  static LocalObjectivesPostgre? _parseLocalObjectivesPostgre(
      Map<String, dynamic> json) {
    try {
      return LocalObjectivesPostgre(
        loId: json['lo_id'],
        name: json['name'],
        functionType: json['function_type'],
        goId: json['go_id'],
        description: json['description'],
        naturalLanguage: json['natural_language'],
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString())
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString())
            : null,
        version: json['version'],
        tenantId: json['tenant_id'],
        workflowSource: json['workflow_source'],
        versionType: json['version_type'],
        autoId: json['auto_id'],
        agentType: json['agent_type'],
        executionRights: json['execution_rights'],
        terminal: json['terminal'],
        tableName: json['table_name'],
        goName: json['go_name'],
      );
    } catch (e) {
      debugPrint('Error parsing LocalObjectivesPostgre: $e');
      return null;
    }
  }

  static Summary? _parseSummary(Map<String, dynamic> json) {
    try {
      return Summary(
        totalItemsPostgres: json['total_items_postgres'],
        totalItemsMongo: json['total_items_mongo'],
        totalItemsCombined: json['total_items_combined'],
        tenantExistsPostgres: json['tenant_exists_postgres'],
        tenantExistsMongo: json['tenant_exists_mongo'],
      );
    } catch (e) {
      debugPrint('Error parsing Summary: $e');
      return null;
    }
  }

  static Metadata? _parseMetadata(Map<String, dynamic> json) {
    try {
      return Metadata(
        retrievedAt: json['retrieved_at'] != null
            ? DateTime.tryParse(json['retrieved_at'].toString())
            : null,
        dataSources: json['data_sources'] != null
            ? List<String>.from(json['data_sources'])
            : null,
        categoriesIncluded: json['categories_included'] != null
            ? List<String>.from(json['categories_included'])
            : null,
      );
    } catch (e) {
      debugPrint('Error parsing Metadata: $e');
      return null;
    }
  }

  // Get all library data
  Future<GetAllMylibrarylist?> getAllLibraryData() async {
    try {
      // Get tenant ID from auth service if not provided
      final savedData = await _authService.getSavedAuthData();
      final finalTenantId = savedData.data?.user?.tenantId ?? '';

      if (finalTenantId.isEmpty) {
        Logger.error('Tenant ID is required for library data');
        return null;
      }

      final response = await _dio.get(
        '$_libraryUrl/$finalTenantId',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      Logger.info('Library data API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return _parseGetAllMylibrarylist(response.data);
      } else {
        Logger.error('Failed to load library data: ${response.statusCode}');
        Logger.error('Response body: ${response.data}');
        return null;
      }
    } catch (e, s) {
      Logger.error('Error getting library data: $e $s');
      return null;
    }
  }

  // Get entities
  Future<Entities?> getEntities({
    String? tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      return response?.data?.dataLibrary?.entities;
    } catch (e) {
      debugPrint('Error getting entities: $e');
      return null;
    }
  }

  // Get roles
  Future<Roles?> getRoles({
    String? tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      return response?.data?.dataLibrary?.roles;
    } catch (e) {
      debugPrint('Error getting roles: $e');
      return null;
    }
  }

  // Get entity attributes
  Future<EntityAttributes?> getEntityAttributes({
    String? tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      return response?.data?.dataLibrary?.entityAttributes;
    } catch (e) {
      debugPrint('Error getting entity attributes: $e');
      return null;
    }
  }

  // Get attributes for a specific entity
  Future<List<EntityAttributesMongoDraft>?> getAttributesForEntity({
    String? tenantId,
    required String entityId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      final allAttributes = <EntityAttributesMongoDraft>[];

      // Add postgres attributes
      if (response?.data?.dataLibrary?.entityAttributes?.postgres != null) {
        allAttributes
            .addAll(response!.data!.dataLibrary!.entityAttributes!.postgres!);
      }

      // Add mongo draft attributes
      if (response?.data?.dataLibrary?.entityAttributes?.mongoDrafts != null) {
        allAttributes.addAll(
            response!.data!.dataLibrary!.entityAttributes!.mongoDrafts!);
      }

      return allAttributes.where((attr) => attr.entityId == entityId).toList();
    } catch (e) {
      debugPrint('Error getting attributes for entity: $e');
      return null;
    }
  }

  // Search entities by name
  Future<List<EntitiesMongoDraft>?> searchEntities({
    String? tenantId,
    required String searchQuery,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      final allEntities = <EntitiesMongoDraft>[];

      // Add postgres entities
      if (response?.data?.dataLibrary?.entities?.postgres != null) {
        allEntities.addAll(response!.data!.dataLibrary!.entities!.postgres!);
      }

      // Add mongo draft entities
      if (response?.data?.dataLibrary?.entities?.mongoDrafts != null) {
        allEntities.addAll(response!.data!.dataLibrary!.entities!.mongoDrafts!);
      }

      return allEntities
          .where((entity) =>
              (entity.name?.toLowerCase().contains(searchQuery.toLowerCase()) ??
                  false) ||
              (entity.displayName
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false))
          .toList();
    } catch (e) {
      debugPrint('Error searching entities: $e');
      return null;
    }
  }

  // Check if tenant exists
  Future<bool> checkTenantExists({
    String? tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      final summary = response?.data?.summary;
      return (summary?.tenantExistsPostgres ?? false) ||
          (summary?.tenantExistsMongo ?? false);
    } catch (e) {
      debugPrint('Error checking tenant exists: $e');
      return false;
    }
  }

  // Get roles list for display
  Future<List<String>?> getRolesList({
    String? tenantId,
    String? token,
  }) async {
    try {
      final rolesResponse = await getRoles(
        tenantId: tenantId,
        token: token,
      );

      final rolesList = <String>[];

      // Add postgres roles
      if (rolesResponse?.postgres != null) {
        for (final role in rolesResponse!.postgres!) {
          if (role.name != null) {
            rolesList.add(role.name!);
          }
        }
      }

      return rolesList.isNotEmpty ? rolesList : null;
    } catch (e) {
      debugPrint('Error getting roles list: $e');
      return null;
    }
  }

  // Get entities list for display
  Future<List<Map<String, dynamic>>?> getEntitiesList({
    String? tenantId,
    String? token,
  }) async {
    try {
      final entitiesResponse = await getEntities(
        tenantId: tenantId,
        token: token,
      );

      final entitiesList = <Map<String, dynamic>>[];

      // Add postgres entities
      if (entitiesResponse?.postgres != null) {
        for (final entity in entitiesResponse!.postgres!) {
          entitiesList.add({
            'entityId': entity.entityId ?? '',
            'name': entity.name ?? 'Unknown Entity',
            'displayName':
                entity.displayName ?? entity.name ?? 'Unknown Entity',
            'description': entity.description ?? '',
            'type': entity.type ?? 'Unknown',
            'businessDomain': entity.businessDomain ?? '',
            'category': entity.category ?? '',
            'icon': entity.icon ?? '',
            'colourTheme': entity.colourTheme ?? '',
            'tableName': entity.tableName ?? '',
            'version': entity.version ?? 1,
            'tenantId': entity.tenantId ?? '',
            'tenantName': entity.tenantName ?? '',
            'attributes': <String>[],
            'isExpanded': false,
            'attributesLoaded': false,
            'status': 'published', // From postgres = published
          });
        }
      }

      // Add mongo draft entities
      if (entitiesResponse?.mongoDrafts != null) {
        for (final entity in entitiesResponse!.mongoDrafts!) {
          entitiesList.add({
            'entityId': entity.entityId ?? '',
            'name': entity.name ?? 'Unknown Entity',
            'displayName':
                entity.displayName ?? entity.name ?? 'Unknown Entity',
            'description': entity.description ?? '',
            'type': entity.type ?? 'Unknown',
            'businessDomain': entity.businessDomain ?? '',
            'category': entity.category ?? '',
            'icon': entity.icon ?? '',
            'colourTheme': entity.colourTheme ?? '',
            'tableName': entity.tableName ?? '',
            'version': entity.version ?? 1,
            'tenantId': entity.tenantId ?? '',
            'tenantName': entity.tenantName ?? '',
            'attributes': <String>[],
            'isExpanded': false,
            'attributesLoaded': false,
            'status': 'draft', // From mongoDrafts = draft
          });
        }
      }

      return entitiesList.isNotEmpty ? entitiesList : null;
    } catch (e) {
      debugPrint('Error getting entities list: $e');
      return null;
    }
  }

  // Get attributes list for a specific entity
  Future<List<String>?> getAttributesListForEntity({
    String? tenantId,
    required String entityId,
    String? token,
  }) async {
    try {
      final attributesResponse = await getAttributesForEntity(
        tenantId: tenantId,
        entityId: entityId,
        token: token,
      );

      if (attributesResponse != null) {
        return attributesResponse
            .map((attr) => attr.displayName?.isNotEmpty == true
                ? attr.displayName!
                : attr.name ?? 'Unknown Attribute')
            .toList();
      }

      return null;
    } catch (e) {
      debugPrint('Error getting attributes list for entity $entityId: $e');
      return null;
    }
  }

  // Get global objectives
  Future<GlobalObjectives?> getGlobalObjectives({
    String? tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData();

      return response?.data?.dataLibrary?.globalObjectives;
    } catch (e) {
      debugPrint('Error getting global objectives: $e');
      return null;
    }
  }

  // Get global objectives list for display
  Future<List<String>?> getGlobalObjectivesList({
    String? tenantId,
    String? token,
  }) async {
    try {
      final globalObjectivesResponse = await getGlobalObjectives(
        tenantId: tenantId,
        token: token,
      );

      final goList = <String>[];

      // Add postgres global objectives
      if (globalObjectivesResponse?.postgres != null) {
        for (final go in globalObjectivesResponse!.postgres!) {
          if (go.name != null) {
            goList.add(go.name!);
          }
        }
      }

      return goList.isNotEmpty ? goList : null;
    } catch (e) {
      debugPrint('Error getting global objectives list: $e');
      return null;
    }
  }

  // Test method to validate PostgresRecordElement parsing
  static PostgresRecordElement? testParsePostgresRecordElement(
      Map<String, dynamic> testData) {
    debugPrint('Testing PostgresRecordElement parsing with data: $testData');
    final result = _parsePostgresRecordElement(testData);
    if (result != null) {
      debugPrint('Successfully parsed PostgresRecordElement: ${result.id}');
    } else {
      debugPrint('Failed to parse PostgresRecordElement');
    }
    return result;
  }

  // Validation method to check if PostgresRecordElement data structure is valid
  static bool validatePostgresRecordElementData(Map<String, dynamic> json) {
    try {
      // Check for required fields that commonly cause parsing errors
      final requiredFields = ['id', 'name'];
      final missingFields = <String>[];

      for (final field in requiredFields) {
        if (!json.containsKey(field) || json[field] == null) {
          missingFields.add(field);
        }
      }

      if (missingFields.isNotEmpty) {
        debugPrint(
            'Missing required fields in PostgresRecordElement: $missingFields');
        return false;
      }

      // Check for data type issues
      if (json['version'] != null &&
          json['version'] is! int &&
          int.tryParse(json['version'].toString()) == null) {
        debugPrint(
            'Invalid version format in PostgresRecordElement: ${json['version']}');
      }

      if (json['mongo_draft_id'] != null &&
          json['mongo_draft_id'] is! int &&
          int.tryParse(json['mongo_draft_id'].toString()) == null) {
        debugPrint(
            'Invalid mongo_draft_id format in PostgresRecordElement: ${json['mongo_draft_id']}');
      }

      return true;
    } catch (e) {
      debugPrint('Error validating PostgresRecordElement data: $e');
      return false;
    }
  }

  // ========== DYNAMIC ATTRIBUTES FUNCTIONALITY ==========
  //
  // This section provides dynamic dropdown functionality for entity attributes.
  // All methods return formatted strings in "EntityName - AttributeName" format
  // to provide better context in dropdown selections.
  //
  // Status-based filtering:
  // - getPublishedAttributeNames(): Only published entities + non-DRAFT attributes
  // - getAllAttributeNames(): All entities + all attributes (with status labels)
  //
  // Example output: "Loan Application - Application ID", "Customer Profile - Customer Name"
  //
  //
  // This section provides dynamic dropdown functionality for entity attributes.
  // All methods return formatted strings in "EntityName - AttributeName" format
  // to provide better context in dropdown selections.
  //
  // Example output: "Loan Application - Application ID", "Customer Profile - Customer Name"
  //

  /// Initialize the service with library data for dynamic attributes
  /// Gets tenant ID from logged-in user, falls back to provided tenantId or 'T1008'
  static Future<bool> initializeForDynamicAttributes({String? tenantId}) async {
    try {
      AuthService _authService = AuthService();

      final savedAuthData = await _authService.getSavedAuthData();
      final userTenantId = savedAuthData.data?.user?.tenantId;
      final service = MyLibraryService();
      final libraryResponse = await service.getAllLibraryData();

      if (libraryResponse?.success == true &&
          libraryResponse?.data?.dataLibrary != null) {
        _cachedLibraryData = libraryResponse!.data!.dataLibrary!;

        // Clear cached attribute names to force refresh
        _cachedPublishedAttributeNames = null;
        _cachedAllAttributeNames = null;
        _isInitialized = true;

        debugPrint(
            'MyLibraryService: Library data loaded successfully for dynamic attributes with tenant ID: $userTenantId');
        return true;
      } else {
        debugPrint(
            'MyLibraryService: Failed to load library data for dynamic attributes with tenant ID: $userTenantId');
        return false;
      }
    } catch (e) {
      debugPrint(
          'MyLibraryService: Error loading library data for dynamic attributes: $e');
      return false;
    }
  }

  /// Get all published entity attribute names for dropdown options
  /// Returns formatted strings in "EntityName - AttributeName" format
  /// ONLY includes published entities (from postgres collection) and their attributes (from postgres collection)
  /// This follows the same logic as web_left_panel_solution.dart where postgres = published, mongoDrafts = draft
  static List<String> getPublishedAttributeNames() {
    if (_cachedPublishedAttributeNames != null) {
      debugPrint(
          'MyLibraryService: Returning cached published attribute names (${_cachedPublishedAttributeNames!.length} items)');
      return _cachedPublishedAttributeNames!;
    }

    debugPrint(
        'MyLibraryService: Building published attribute names from library data...');
    final attributeNames = <String>{};

    // Create a map of entityId to entity name for quick lookup (PUBLISHED ENTITIES ONLY)
    // Published entities = entities from postgres collection (regardless of status field)
    final publishedEntityMap = <String, String>{};

    // Process ONLY entities from postgres collection (these are published by definition)
    if (_cachedLibraryData?.entities?.postgres != null) {
      for (final entity in _cachedLibraryData!.entities!.postgres!) {
        if (entity.entityId != null) {
          final entityName = entity.displayName?.isNotEmpty == true
              ? entity.displayName!
              : entity.name ?? 'Unknown Entity';
          publishedEntityMap[entity.entityId!] = entityName;
          debugPrint(
              'Added published entity: $entityName (ID: ${entity.entityId})');
        }
      }
    }

    debugPrint('Found ${publishedEntityMap.length} published entities');

    // Process ONLY PUBLISHED ATTRIBUTES from postgres collection
    // Published attributes = attributes from postgres collection (regardless of status field)
    if (_cachedLibraryData?.entityAttributes?.postgres != null) {
      for (final attribute in _cachedLibraryData!.entityAttributes!.postgres!) {
        if (attribute.entityId != null) {
          final attributeName = attribute.displayName?.isNotEmpty == true
              ? attribute.displayName!
              : attribute.name;

          if (attributeName != null && attributeName.isNotEmpty) {
            // ONLY include attributes that belong to published entities
            final entityName = publishedEntityMap[attribute.entityId!];

            if (entityName != null) {
              // Format as "EntityName - AttributeName" (space-dash-space separator)
              final formattedName = '$entityName - $attributeName';
              attributeNames.add(formattedName);
              debugPrint('Added published attribute: $formattedName');
            }
          }
        }
      }
    }

    // NOTE: We do NOT process mongoDrafts collections here because:
    // - mongoDrafts entities = draft entities (not published)
    // - mongoDrafts attributes = draft attributes (not published)
    // This follows the same logic as web_left_panel_solution.dart

    _cachedPublishedAttributeNames = attributeNames.toList()..sort();

    debugPrint(
        'MyLibraryService: Found ${publishedEntityMap.length} published entities');
    debugPrint(
        'MyLibraryService: Generated ${_cachedPublishedAttributeNames!.length} published attribute names');
    if (_cachedPublishedAttributeNames!.isNotEmpty) {
      debugPrint(
          'MyLibraryService: Sample published attributes: ${_cachedPublishedAttributeNames!.take(5).join(", ")}');
    }

    return _cachedPublishedAttributeNames!;
  }

  /// Get all entity attribute names (both published and draft) for dropdown options
  /// Returns formatted strings in "EntityName - AttributeName" format
  static List<String> getAllAttributeNames() {
    if (_cachedAllAttributeNames != null) {
      return _cachedAllAttributeNames!;
    }

    final attributeNames = <String>{};

    // Create a map of entityId to entity name for quick lookup (published entities)
    final entityMap = <String, String>{};
    if (_cachedLibraryData?.entities?.postgres != null) {
      for (final entity in _cachedLibraryData!.entities!.postgres!) {
        if (entity.entityId != null) {
          final entityName = entity.displayName?.isNotEmpty == true
              ? entity.displayName!
              : entity.name ?? 'Unknown Entity';
          entityMap[entity.entityId!] = entityName;
        }
      }
    }

    // Add draft entities to the map
    if (_cachedLibraryData?.entities?.mongoDrafts != null) {
      for (final entity in _cachedLibraryData!.entities!.mongoDrafts!) {
        if (entity.entityId != null) {
          final entityName = entity.displayName?.isNotEmpty == true
              ? entity.displayName!
              : entity.name ?? 'Unknown Entity';
          entityMap[entity.entityId!] = entityName;
        }
      }
    }

    // Add published attributes (postgres) - but exclude those with DRAFT status
    if (_cachedLibraryData?.entityAttributes?.postgres != null) {
      for (final attribute in _cachedLibraryData!.entityAttributes!.postgres!) {
        final attributeName = attribute.displayName?.isNotEmpty == true
            ? attribute.displayName!
            : attribute.name;

        if (attributeName != null && attributeName.isNotEmpty) {
          // Get entity name from the map
          final entityName = attribute.entityId != null
              ? entityMap[attribute.entityId!] ?? 'Unknown Entity'
              : 'Unknown Entity';

          // Determine status for labeling
          final isDraft = attribute.status?.toString() == 'FluffyStatus.DRAFT';
          final statusLabel = isDraft ? ' (Draft)' : '';

          // Format as "EntityName - AttributeName" with optional status label
          final formattedName = '$entityName - $attributeName$statusLabel';
          attributeNames.add(formattedName);
        }
      }
    }

    // Add draft attributes (mongoDrafts) - always labeled as draft
    if (_cachedLibraryData?.entityAttributes?.mongoDrafts != null) {
      for (final attribute
          in _cachedLibraryData!.entityAttributes!.mongoDrafts!) {
        final attributeName = attribute.displayName?.isNotEmpty == true
            ? attribute.displayName!
            : attribute.name;

        if (attributeName != null && attributeName.isNotEmpty) {
          // Get entity name from the map
          final entityName = attribute.entityId != null
              ? entityMap[attribute.entityId!] ?? 'Unknown Entity'
              : 'Unknown Entity';

          // Format as "EntityName - AttributeName (Draft)" for draft attributes
          final formattedName = '$entityName - $attributeName (Draft)';
          attributeNames.add(formattedName);
        }
      }
    }

    _cachedAllAttributeNames = attributeNames.toList()..sort();
    return _cachedAllAttributeNames!;
  }

  /// Get attribute names for a specific entity
  /// Returns formatted strings in "EntityName - AttributeName" format
  static List<String> getAttributeNamesForEntityDynamic(String entityId,
      {bool publishedOnly = true}) {
    final attributeNames = <String>[];

    if (_cachedLibraryData?.entityAttributes != null) {
      // Get entity name for the specified entityId
      String entityName = 'Unknown Entity';

      // Look for entity in published entities first
      if (_cachedLibraryData?.entities?.postgres != null) {
        for (final entity in _cachedLibraryData!.entities!.postgres!) {
          if (entity.entityId == entityId) {
            entityName = entity.displayName?.isNotEmpty == true
                ? entity.displayName!
                : entity.name ?? 'Unknown Entity';
            break;
          }
        }
      }

      // If not found and not publishedOnly, look in draft entities
      if (entityName == 'Unknown Entity' &&
          !publishedOnly &&
          _cachedLibraryData?.entities?.mongoDrafts != null) {
        for (final entity in _cachedLibraryData!.entities!.mongoDrafts!) {
          if (entity.entityId == entityId) {
            entityName = entity.displayName?.isNotEmpty == true
                ? entity.displayName!
                : entity.name ?? 'Unknown Entity';
            break;
          }
        }
      }

      final allAttributes = <EntityAttributesMongoDraft>[];

      // Add published attributes if requested
      if (_cachedLibraryData!.entityAttributes!.postgres != null) {
        allAttributes.addAll(_cachedLibraryData!.entityAttributes!.postgres!);
      }

      // Add draft attributes if not publishedOnly
      if (!publishedOnly &&
          _cachedLibraryData!.entityAttributes!.mongoDrafts != null) {
        allAttributes
            .addAll(_cachedLibraryData!.entityAttributes!.mongoDrafts!);
      }

      // Filter attributes for the specific entity
      final entityAttributes =
          allAttributes.where((attr) => attr.entityId == entityId).toList();

      for (final attribute in entityAttributes) {
        // If publishedOnly is true, skip attributes with DRAFT status
        if (publishedOnly &&
            attribute.status?.toString() == 'FluffyStatus.DRAFT') {
          continue;
        }

        final attributeName = attribute.displayName?.isNotEmpty == true
            ? attribute.displayName!
            : attribute.name;

        if (attributeName != null && attributeName.isNotEmpty) {
          // Determine status for labeling (only for non-publishedOnly mode)
          final isDraft = attribute.status?.toString() == 'FluffyStatus.DRAFT';
          final statusLabel = (!publishedOnly && isDraft) ? ' (Draft)' : '';

          // Format as "EntityName - AttributeName" with optional status label
          final formattedName = '$entityName - $attributeName$statusLabel';
          attributeNames.add(formattedName);
        }
      }
    }

    return attributeNames..sort();
  }

  /// Check if the service has been initialized with library data
  static bool get isInitializedForDynamicAttributes =>
      _isInitialized && _cachedLibraryData != null;

  /// Get fallback attribute names when service is not initialized
  /// Returns entity-attribute options in "EntityName - AttributeName" format
  static List<String> getFallbackAttributeNames() {
    return [
      'Customer - ID',
      'Customer - Name',
      'Customer - Email',
      'Customer - Phone',
      'Customer - Address',
      'Order - ID',
      'Order - Amount',
      'Order - Date',
      'Order - Status',
      'Payment - ID',
      'Payment - Status',
      'Payment - Method',
      'Payment - Amount',
      'Product - ID',
      'Product - Name',
      'Product - Price',
      'Product - Category',
      'Invoice - ID',
      'Invoice - Number',
      'Invoice - Total',
    ];
  }

  /// Clear all cached data (useful for refresh scenarios)
  static void clearDynamicAttributesCache() {
    _cachedLibraryData = null;
    _cachedPublishedAttributeNames = null;
    _cachedAllAttributeNames = null;
    _isInitialized = false;
  }
}
