import 'package:flutter/foundation.dart';

/// Provider to track the availability and accessibility of LoInputStackAccordion
/// This helps other components (like WebLeftPanelSolution) know when they can
/// interact with the accordion component
class AccordionAvailabilityProvider extends ChangeNotifier {
  bool _isAccordionAvailable = false;
  bool _isAccordionExpanded = false;
  String? _currentScreenContext;

  /// Whether the LoInputStackAccordion is present and accessible
  bool get isAccordionAvailable => _isAccordionAvailable;

  /// Whether the accordion is currently expanded (if available)
  bool get isAccordionExpanded => _isAccordionExpanded;

  /// Current screen context where the accordion is available
  String? get currentScreenContext => _currentScreenContext;

  /// Whether add actions should be enabled (accordion is available and accessible)
  bool get canAddToAccordion => _isAccordionAvailable;

  /// Set the accordion availability status
  void setAccordionAvailability({
    required bool isAvailable,
    bool isExpanded = false,
    String? screenContext,
  }) {
    bool hasChanged = _isAccordionAvailable != isAvailable ||
        _isAccordionExpanded != isExpanded ||
        _currentScreenContext != screenContext;

    if (hasChanged) {
      _isAccordionAvailable = isAvailable;
      _isAccordionExpanded = isExpanded;
      _currentScreenContext = screenContext;
      notifyListeners();
    }
  }

  /// Mark accordion as available (called when accordion is rendered)
  void markAccordionAvailable({
    bool isExpanded = false,
    String? screenContext,
  }) {
    setAccordionAvailability(
      isAvailable: true,
      isExpanded: isExpanded,
      screenContext: screenContext,
    );
  }

  /// Mark accordion as unavailable (called when accordion is disposed or not rendered)
  void markAccordionUnavailable() {
    setAccordionAvailability(
      isAvailable: false,
      isExpanded: false,
      screenContext: null,
    );
  }

  /// Update accordion expansion state
  void updateAccordionExpansion(bool isExpanded) {
    if (_isAccordionAvailable && _isAccordionExpanded != isExpanded) {
      _isAccordionExpanded = isExpanded;
      notifyListeners();
    }
  }

  /// Get user-friendly message for why add action is disabled
  String getDisabledMessage() {
    if (!_isAccordionAvailable) {
      return 'Input Stack Accordion is not available on this screen';
    }
    return 'Cannot add to accordion at this time';
  }

  /// Clear all state (useful for testing or reset scenarios)
  void reset() {
    _isAccordionAvailable = false;
    _isAccordionExpanded = false;
    _currentScreenContext = null;
    notifyListeners();
  }
}
