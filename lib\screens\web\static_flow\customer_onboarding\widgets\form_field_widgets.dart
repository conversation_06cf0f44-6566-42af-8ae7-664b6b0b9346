import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';

/// Form field widget with consistent styling
class FormFieldWidget extends StatelessWidget {
  final String label;
  final String hintText;
  final bool isRequired;
  final TextEditingController? controller;
  final String? initialValue;
  final bool obscureText;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final int? maxLines;

  const FormFieldWidget({
    super.key,
    required this.label,
    required this.hintText,
    this.isRequired = false,
    this.controller,
    this.initialValue,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.suffixIcon,
    this.prefixIcon,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(context),
        SizedBox(height: AppSpacing.xs),
        _buildTextField(context),
      ],
    );
  }

  /// Build label with required indicator
  Widget _buildLabel(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: label,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF242424),
        ),
        children: isRequired
            ? [
                TextSpan(
                  text: ' *',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                ),
              ]
            : null,
      ),
    );
  }

  /// Build text field with consistent styling
  Widget _buildTextField(BuildContext context) {
    return TextFormField(
      controller: controller,
      initialValue: initialValue,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator ?? (isRequired ? _defaultValidator : null),
      onChanged: onChanged,
      enabled: enabled,
      maxLines: maxLines,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF9CA3AF),
        ),
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: enabled ? Colors.white : Colors.grey[100],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF0058FF), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
      ),
    );
  }

  /// Default validator for required fields
  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '$label is required';
    }
    return null;
  }
}

/// Dropdown field widget
class DropdownFieldWidget extends StatelessWidget {
  final String label;
  final String hintText;
  final bool isRequired;
  final String? value;
  final List<String> options;
  final void Function(String?)? onChanged;
  final String? Function(String?)? validator;
  final bool enabled;

  const DropdownFieldWidget({
    super.key,
    required this.label,
    required this.hintText,
    required this.options,
    this.isRequired = false,
    this.value,
    this.onChanged,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(context),
        SizedBox(height: AppSpacing.xs),
        _buildDropdown(context),
      ],
    );
  }

  /// Build label with required indicator
  Widget _buildLabel(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: label,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF242424),
        ),
        children: isRequired
            ? [
                TextSpan(
                  text: ' *',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                ),
              ]
            : null,
      ),
    );
  }

  /// Build dropdown with consistent styling
  Widget _buildDropdown(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: enabled ? onChanged : null,
      validator: validator ?? (isRequired ? _defaultValidator : null),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF9CA3AF),
        ),
        filled: true,
        fillColor: enabled ? Colors.white : Colors.grey[100],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF0058FF), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
      ),
      items: options.map((String option) {
        return DropdownMenuItem<String>(
          value: option,
          child: Text(
            option,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF242424),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Default validator for required fields
  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '$label is required';
    }
    return null;
  }
}
