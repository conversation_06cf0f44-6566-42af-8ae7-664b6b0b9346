import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/all_smart_resolution_object_mobile.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/create_object_mobile.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';

class IndustryBundlesObjectMobile extends StatefulWidget {
  const IndustryBundlesObjectMobile({super.key});

  @override
  State<IndustryBundlesObjectMobile> createState() =>
      _IndustryBundlesObjectMobileState();
}

class _IndustryBundlesObjectMobileState
    extends State<IndustryBundlesObjectMobile> {
  bool _showCloseIcon = true; // Show close icon when screen opens
  bool _showCloseIconSmart = false; // Show sparkle icon initially

  // JSON data for the industry bundles
  final Map<String, dynamic> bundlesData = {
    "header": {
      "title": "Industry Bundles",
    },
    "bundles": [
      {
        "id": 1,
        "title": "E-Commerce Complete",
        "subtitle": "23 templates",
        "description":
            "Full e-commerce customer entity with privacy, UX, and business intelligence",
        "matchPercentage": "92% match",
        "hasViewButton": true,
        "hasApplyButton": true
      },
      {
        "id": 2,
        "title": "Security First",
        "subtitle": "18 templates",
        "description": "Enhanced security and compliance for sensitive data",
        "matchPercentage": "92% match",
        "hasViewButton": true,
        "hasApplyButton": true
      },
      {
        "id": 3,
        "title": "E-Commerce Complete",
        "subtitle": "23 templates",
        "description":
            "Full e-commerce customer entity with privacy, UX, and business intelligence",
        "matchPercentage": "92% match",
        "hasViewButton": true,
        "hasApplyButton": true
      }
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: const CustomDrawer(),
      backgroundColor: const Color(0xFFFfffff),
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildBundlesList(),
                ),
              ],
            ),
            // Bottom action buttons positioned on top
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomActions(context),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFFF7F9FB),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF835BED), Color(0xFF7540E5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            'assets/images/object-flow-mobile/industry-bundles.svg', // Update with your SVG asset path
            width: 28,
            height: 28,
          ),
          const SizedBox(width: 8),
          Text(
            bundlesData['header']['title'],
            style: FontManager.getCustomStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBundlesList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        itemCount: bundlesData['bundles'].length,
        itemBuilder: (context, index) {
          final bundle = bundlesData['bundles'][index];
          return _buildBundleCard(
            bundle['title'],
            bundle['subtitle'],
            bundle['description'],
            bundle['matchPercentage'],
          );
        },
      ),
    );
  }

  Widget _buildBundleCard(
    String title,
    String subtitle,
    String description,
    String matchPercentage,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFAFAFAF),
          width: .5,
        ),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            // Left purple accent bar
            Container(
              width: 5,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF835BED), Color(0xFF7540E5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            // Content
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row with title/subtitle and match percentage
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                title,
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                  fontFamily: FontManager.fontFamilyInter,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                subtitle,
                                style: FontManager.getCustomStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w300,
                                  color: const Color(0xFF666666),
                                  fontFamily: FontManager.fontFamilyInter,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(
                                0xFFE5DDFA), // Your background color
                            borderRadius:
                                BorderRadius.circular(4), // Rounded corners
                          ),
                          child: Text(
                            matchPercentage,
                            style: FontManager.getCustomStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w300,
                              color: Colors.black, // Use white for contrast
                              fontFamily: FontManager.fontFamilyInter,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Description
                    Text(
                      description,
                      style: FontManager.getCustomStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF000000),
                        fontFamily: FontManager.fontFamilyInter,
                      ),
                    ),
                    const SizedBox(height: 14),
                    // Buttons row
                    Row(
                      children: [
                        // VIEW button
                        SizedBox(
                          width: 80,
                          child: OutlinedButton(
                            onPressed: () {
                              // Add view functionality
                            },
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(
                                color: Color(0xFFD0D0D0),
                                width: 1,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'VIEW',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: const Color(0xFf000000),
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                          ),
                        ),
                        const Spacer(),
                        // APPLY ALL button
                        SizedBox(
                          width: 100,
                          child: ElevatedButton(
                            onPressed: () {
                              // Add apply all functionality
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0066FF),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              elevation: 0,
                            ),
                            child: Text(
                              'APPLY ALL',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: Colors.white,
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Left side icons in one container - stacked vertically
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const CreateObjectScreenMobile(),
                  // ...existing code...
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const begin = Offset(1.0, 0.0); // Slide from right
                    const end = Offset.zero;
                    const curve = Curves.easeInOut;
                    final tween = Tween(begin: begin, end: end)
                        .chain(CurveTween(curve: curve));
                    return SlideTransition(
                      position: animation.drive(tween),
                      child: child,
                    );
                  },
                  transitionDuration: const Duration(milliseconds: 600),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Color(0xAAD0D0D0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    offset: const Offset(0, 3), // X: 0, Y: 3
                    blurRadius: 20, // Blur: 20
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.menu,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(height: 8),
                  Icon(
                    Icons.mic_none,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                ],
              ),
            ),
          ),

          const Spacer(),

          // Right side colored circles - stacked vertically
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  if (_showCloseIconSmart) {
                    Navigator.pop(context);
                  } else {
                    setState(() {
                      _showCloseIconSmart = true;
                    });
                    Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  AllSmartResolutionObjectMobile(),
                          transitionDuration: Duration(milliseconds: 100),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) =>
                                  FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        )).then((_) {
                      // Reset the icon when returning from the screen
                      setState(() {
                        _showCloseIconSmart = false;
                      });
                    });
                  }
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF064CD1), Color(0xFF0093FF)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Center(
                    child: _showCloseIconSmart
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          )
                        : SvgPicture.asset(
                            'assets/images/object-flow-mobile/sparkle-stars-ai.svg', // Update with your SVG asset path
                            width: 30,
                            height: 30,
                          ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  // Since we're already on the industry bundles screen and showing close icon, just close
                  Navigator.pop(context);
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFF673AB7),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ], // Purple color
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Center(
                    child: _showCloseIcon
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          )
                        : SvgPicture.asset(
                            'assets/images/object-flow-mobile/industry-bundles.svg', // Update with your SVG asset path
                            width: 30,
                            height: 30,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
