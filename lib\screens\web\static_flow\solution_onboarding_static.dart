import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/models/customer_model.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:provider/provider.dart';

// Import the SolutionModel from the main file
import 'extract_details_middle_static.dart';

// Import Customer Onboarding components for accordion structure
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/accordion_item_widget.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/constants/customer_onboarding_constants.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/hover_bell_icon.dart';

class SolutionOnboardingStatic extends StatefulWidget {
  final List<SolutionModel>? solutions;
  final List<ObjectCreationModel>? objects;

  const SolutionOnboardingStatic({
    super.key,
    this.solutions,
    this.objects,
  });

  @override
  State<SolutionOnboardingStatic> createState() =>
      _SolutionOnboardingStaticState();
}

class _SolutionOnboardingStaticState extends State<SolutionOnboardingStatic> {
  final List<bool> _isExpanded = [
    false,
    false,
    false
  ]; // Control expansion states

  late AccordionController _accordionController;
  bool _showProcessSteps = false; // Control process steps visibility

  // Business rules data - same as CustomerOnboardingStatic
  List<BusinessRule1> _businessRulesData = [];

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });
    _initializeBusinessRules();
  }

  /// Initialize business rules data - same as CustomerOnboardingStatic
  void _initializeBusinessRules() {
    _businessRulesData = List.from(CustomerOnboardingConstants.defaultBusinessRules);
  }

  /// Update rule by field - same as CustomerOnboardingStatic
  void updateRuleByField(String field, BusinessRule1 updatedRule) {
    final index = _businessRulesData.indexWhere((rule) => rule.field == field);
    if (index != -1) {
      setState(() {
        _businessRulesData[index] = updatedRule;
      });
    }
  }

  @override
  void dispose() {
    _accordionController.dispose();
    super.dispose();
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) {
      return ResponsiveFontSizes.titleSmall(context);
    } else if (screenWidth > 800) {
      return ResponsiveFontSizes.bodyLarge(context);
    } else {
      return ResponsiveFontSizes.bodyMedium(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Show solution components for each solution
        if (widget.solutions != null && widget.solutions!.isNotEmpty)
          ...widget.solutions!
              .map((solution) => _buildAdvancedSolutionExpansionPanel(
                    context,
                    'Solution: ${solution.name}',
                    solutionData: solution,
                  ))
              .toList()
        else
          // Default static solution when no solutions are added
          _buildAdvancedSolutionExpansionPanel(
            context,
            'Solution: Customer Onboarding',
            solutionData: null,
          ),
      ],
    );
  }

  Widget _buildAdvancedSolutionExpansionPanel(
      BuildContext context, String solutionTitle,
      {SolutionModel? solutionData}) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final isExpanded = provider.isObjectExpanded(solutionTitle);

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            // borderRadius: BorderRadius.circular(4),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ListTileTheme(
              dense: true, // slightly tighter by default
              child: ExpansionTile(
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                childrenPadding: EdgeInsets.zero,
                onExpansionChanged: (expanded) =>
                    provider.setObjectExpansion(solutionTitle, expanded),
                trailing: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isExpanded
                        ? const Color(0xFF0058FF)
                        : Colors.transparent,
                  ),
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isExpanded ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        solutionTitle,
                        style: FontManager.getCustomStyle(
                          fontSize: _getResponsiveValueFontSize(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: isExpanded ? Colors.black : Color(0xFF242424),
                          fontWeight:
                              isExpanded ? FontWeight.w600 : FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    HoverBellIcon(
                      onTap: () {
                        // Bell icon click action - can be customized as needed
                      },
                    ),
                  ],
                ),
                children: [
                  _buildSolutionDetailsSection(context, solutionTitle,
                      solutionData: solutionData),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSolutionDetailsSection(
      BuildContext context, String solutionTitle,
      {SolutionModel? solutionData}) {
    // Use the first object data if available, same as CustomerOnboardingStatic
    final objectData =
        widget.objects?.isNotEmpty == true ? widget.objects!.first : null;

    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Level 1: Solution subheading (clickable) - styled like ExpansionTile
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: InkWell(
              onTap: () {
                setState(() {
                  _showProcessSteps = !_showProcessSteps;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        solutionData?.name ?? 'Customer Onboarding',
                        style: FontManager.getCustomStyle(
                          fontSize: _getResponsiveValueFontSize(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: _showProcessSteps
                              ? Colors.black
                              : const Color(0xFF242424),
                          fontWeight: _showProcessSteps
                              ? FontWeight.w600
                              : FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                    ),
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: _showProcessSteps
                            ? const Color(0xFF0058FF)
                            : Colors.transparent,
                      ),
                      child: Icon(
                        _showProcessSteps
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color:
                            _showProcessSteps ? Colors.white : Colors.grey[600],
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Level 1 or Level 2 content based on state
          if (_showProcessSteps)
            _buildProcessStepsView(objectData)
          else
            _buildStaticRulesView(),
        ],
      ),
    );
  }

  /// Level 1: Build static rules view (8 text points) - exact copy from CustomerOnboardingStatic
  Widget _buildStaticRulesView() {
    const List<String> staticRules = [
      'System Verifies Budget Availability.',
      'Requester Creates Purchase Requisition.',
      'System Validates Purchase Requisition.',
      'Director Reviews High-Value Purchase Requisition.',
      'Procurement Officer Creates Purchase Order.',
      'System Initiates Parallel Assessment Tasks.',
      'Manager Reviews Purchase Requisition.',
      'System Notifies Requester Of Rejection.',
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: staticRules.asMap().entries.map((entry) {
          final index = entry.key;
          final rule = entry.value;
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${index + 1}.',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    rule,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Level 2: Build process steps view (expansion panels) - exact copy from CustomerOnboardingStatic
  Widget _buildProcessStepsView(ObjectCreationModel? objectData) {
    return Column(
      children: CustomerOnboardingConstants.processSteps.map((step) {
        return AccordionItemWidget(
          title: step['title'] as String,
          showAttributeTable: step['showAttributeTable'] as bool,
          accordionController: _accordionController,
          objectData: objectData,
          businessRulesData:
              step['title'] == 'Core Metadata' ? _businessRulesData : null,
          onUpdateRule: updateRuleByField,
          isExpandEnabled: true,
          isSaved: false,
          // Let the widget auto-determine status based on title
        );
      }).toList(),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable, {
    ObjectCreationModel? objectData,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('simple_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Allow it to take available space but don't force ellipsis
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: _getResponsiveValueFontSize(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow
                                .visible, // Allow text to show fully
                            maxLines: 2, // Allow wrapping to 2 lines if needed
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Status Badge - Give it fixed space
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: title == 'System Verifies Budget Availability.'
                  ? _buildBusinessRulesConfiguration(context,
                      objectData: objectData)
                  : showAttributeTable
                      ? _buildAttributeConfigurationTable(context,
                          objectData: objectData)
                      : _buildPlaceholderContent(context, title,
                          objectData: objectData),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBusinessRulesConfiguration(BuildContext context,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 8),
          // Header with title and Add Rule button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Business Rules Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              Container(
                child: ElevatedButton(
                  onPressed: () {
                    // Simplified dialog - just show a message
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Business Rules Configuration'),
                        content: const Text(
                            'Business rules configuration would be implemented here.'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Close'),
                          ),
                        ],
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0058FF),
                    foregroundColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: Text(
                    'Add Rule',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Business rules table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF9FAFB),
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E7EB)),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'FIELD',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'RULE TYPE',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'CONDITION',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'STATUS',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Sample business rules data
                _buildBusinessRuleRow('Budget Amount', 'Validation',
                    '> 0 AND < 1000000', 'Active'),
                _buildBusinessRuleRow('Approval Level', 'Authorization',
                    'IF amount > 50000 THEN manager_approval', 'Active'),
                _buildBusinessRuleRow('Department Code', 'Validation',
                    'IN (IT, HR, FIN, OPS)', 'Active'),
                _buildBusinessRuleRow(
                    'Request Date', 'Validation', '>= current_date', 'Active'),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildBusinessRuleRow(
      String field, String ruleType, String condition, String status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              field,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              ruleType,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              condition,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: status == 'Active'
                    ? const Color(0xFFDCFCE7)
                    : const Color(0xFFFEF3C7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                status,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: status == 'Active'
                      ? const Color(0xFF065F46)
                      : const Color(0xFF92400E),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeConfigurationTable(BuildContext context,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 8),
          // Header with title
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Attribute Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Sample attribute table content
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF9FAFB),
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E7EB)),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'FIELD',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'VALUE',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'SOURCE',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Use real data from objectData if available, otherwise use sample data
                _buildTableRow(
                    'Entity Name', objectData?.name ?? 'Customer', 'Extracted'),
                _buildTableRow(
                    'Type', objectData?.type ?? 'Master', 'Inferred'),
                _buildTableRow('Business Domain',
                    objectData?.businessDomain ?? 'CRM', 'Extracted'),
                _buildTableRow('Category',
                    objectData?.category ?? 'Customer Data', 'Inferred'),
                _buildTableRow(
                    'Tags',
                    objectData?.tags?.join(', ') ??
                        'customer, profile, personal, banking',
                    'Extracted'),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildTableRow(String field, String value, String source) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              field,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: source == 'Extracted'
                    ? const Color(0xFFDCFCE7)
                    : const Color(0xFFFEF3C7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                source,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: source == 'Extracted'
                      ? const Color(0xFF065F46)
                      : const Color(0xFF92400E),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Text(
        '$title configuration content would go here',
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 14,
        ),
      ),
    );
  }
}

// Shared HoverBellIcon component (reused from CustomerOnboardingStatic)
class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.notifications_outlined,
            size: 18,
            color: isHovered ? Colors.blue : const Color(0xffFF2019),
          ),
        ),
      ),
    );
  }
}
