import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:nsl/models/object_creation_model.dart';

import '../models/solution/go_model.dart';

class LocalObjectiveToTextConverter {
  /// Convert a specific LocalObjective to text format
  static String convertLocalObjectiveToText(
    LocalObjectivesList localObjective,
    GlobalObjectives? globalObjectives,
    List<DataConstraint>? dataConstraints,
    List<Rule>? validationRules,
    String? tenantId,
  ) {
    StringBuffer buffer = StringBuffer();
    //   tenantId = 'FinanceFlow Bank';

    //     buffer.writeln('''
// CreateEmployeeRecord

// name: "CreateEmployeeRecord"
// version: "1.1"
// status: "Active"
// workflow_source: "origin"
// function_type: "Create"
// agent_type: "HUMAN"
// ui_type: "Form"
// execution_rights: "HR Manager"
// Tenant: "FinanceFlow Bank"

// Inputs: Employee with employee_id, name, email, contact_number, blood_group, address, join_date, department, designation, entry_date [timestamp], created_date [timestamp], updated_date [timestamp]

// Employee.employee_id
//     - Source: nested_function
//     - Agent: DIGITAL
//     - Function Reference: generate_employee_id_1
//     - Attribute type: system_generated

// Employee.name
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.email
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.contact_number
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.blood_group
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.address
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.join_date
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.department
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.designation
//     - Source: user
//     - Agent: HUMAN
//     - Attribute type: input

// Employee.entry_date
//     - Source: nested_function
//     - Agent: DIGITAL
//     - Function Reference: current_timestamp_entry_1
//     - Attribute type: system_generated

// Employee.created_date
//     - Source: nested_function
//     - Agent: DIGITAL
//     - Function Reference: current_timestamp_created_1
//     - Attribute type: system_generated

// Employee.updated_date
//     - Source: nested_function
//     - Agent: DIGITAL
//     - Function Reference: current_timestamp_updated_1
//     - Attribute type: system_generated

// Outputs: Employee with employee_id, name, email, contact_number, blood_group, address, join_date, department, designation, entry_date, created_date, updated_date

// Validation Stack

// Employee.employee_id
// - Attribute: Employee.employee_id
// - Validation Function: validate_unique_employee_id
// - Success Value: valid_unique_id
// - Failure Value: null
// - Success Message: "Employee ID generated successfully"
// - Failure Message: "Employee ID generation failed or already exists"

// Employee.name
// - Attribute: Employee.name
// - Validation Function: validate_required_text
// - Success Value: valid_text
// - Failure Value: empty_text
// - Success Message: "Employee name is valid"
// - Failure Message: "Employee name is required and cannot be empty"

// Employee.email
// - Attribute: Employee.email
// - Validation Function: validate_email_format_unique
// - Success Value: valid_unique_email
// - Failure Value: invalid_duplicate_email
// - Success Message: "Email format is valid and unique"
// - Failure Message: "Invalid email format or email already exists"

// Employee.contact_number
// - Attribute: Employee.contact_number
// - Validation Function: validate_phone_format
// - Success Value: valid_phone
// - Failure Value: invalid_phone
// - Success Message: "Contact number format is valid"
// - Failure Message: "Invalid contact number format"

// Employee.blood_group
// - Attribute: Employee.blood_group
// - Validation Function: validate_blood_group_standard
// - Success Value: valid_blood_group
// - Failure Value: invalid_blood_group
// - Success Message: "Blood group is valid"
// - Failure Message: "Invalid blood group (must be A+, A-, B+, B-, AB+, AB-, O+, O-)"

// Employee.address
// - Attribute: Employee.address
// - Validation Function: validate_required_text
// - Success Value: valid_text
// - Failure Value: empty_text
// - Success Message: "Address is valid"
// - Failure Message: "Address is required and cannot be empty"

// Employee.join_date
// - Attribute: Employee.join_date
// - Validation Function: validate_date_logical
// - Success Value: valid_date
// - Failure Value: invalid_date
// - Success Message: "Join date is valid"
// - Failure Message: "Join date must be valid and not in the future"

// Employee.department
// - Attribute: Employee.department
// - Validation Function: validate_department_exists
// - Success Value: valid_department
// - Failure Value: invalid_department
// - Success Message: "Department is valid"
// - Failure Message: "Department must exist in organization structure"

// Employee.designation
// - Attribute: Employee.designation
// - Validation Function: validate_required_text
// - Success Value: valid_text
// - Failure Value: empty_text
// - Success Message: "Designation is valid"
// - Failure Message: "Designation is required and cannot be empty"

// UI Stack

// Employee.employee_id
// - UI Control: label
// - Data Type: text
// - Editable: false
// - Required: true
// - Hidden: false

// Employee.name
// - UI Control: text
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.email
// - UI Control: email
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.contact_number
// - UI Control: mobile_no
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.blood_group
// - UI Control: text
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.address
// - UI Control: multiline
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.join_date
// - UI Control: date
// - Data Type: date
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.department
// - UI Control: text
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.designation
// - UI Control: text
// - Data Type: text
// - Editable: true
// - Required: true
// - Hidden: false

// Employee.entry_date
// - UI Control: label
// - Data Type: datetime
// - Editable: false
// - Required: true
// - Hidden: false

// Employee.created_date
// - UI Control: label
// - Data Type: datetime
// - Editable: false
// - Required: false
// - Hidden: true

// Employee.updated_date
// - UI Control: label
// - Data Type: datetime
// - Editable: false
// - Required: false
// - Hidden: true

// Nested Function Stack:

// Function: generate_employee_id_1
//     - Source Attribute: Employee.employee_id
//     - Function Name: generate_id
//     - Inputs: [prefix='EMP', year='YYYY', month='MM', sequence='sequential']
//     - Conditions: []
//     - Outputs: [Employee.employee_id]

// Function: current_timestamp_entry_1
//     - Source Attribute: Employee.entry_date
//     - Function Name: current_timestamp
//     - Inputs: []
//     - Conditions: []
//     - Outputs: [Employee.entry_date]

// Function: current_timestamp_created_1
//     - Source Attribute: Employee.created_date
//     - Function Name: current_timestamp
//     - Inputs: []
//     - Conditions: []
//     - Outputs: [Employee.created_date]

// Function: current_timestamp_updated_1
//     - Source Attribute: Employee.updated_date
//     - Function Name: current_timestamp
//     - Inputs: []
//     - Conditions: []
//     - Outputs: [Employee.updated_date]

//  Input Mapping:
// - Employee.name -> Employee.name
// - Employee.email -> Employee.email
// - Employee.contact_number -> Employee.contact_number
// - Employee.blood_group -> Employee.blood_group
// - Employee.address -> Employee.address
// - Employee.join_date -> Employee.join_date
// - Employee.department -> Employee.department
// - Employee.designation -> Employee.designation

// Nested Function Mapping

// | Function Name | Source Item | Target GO Name | Target LO Name | Target Nested Function Name | Target Stack | Target Item |
// |---------------|-------------|----------------|----------------|---------------------------|-------------|-------------|
// | generate_employee_id_1 | Employee.employee_id | Manage Employee Records | CreateEmployeeRecord | create_employee_record_1| INPUT | Employee.employee_id |
// | current_timestamp_entry_1 | Employee.entry_date | Manage Employee Records | CreateEmployeeRecord | create_employee_record_1 | INPUT | Employee.entry_date |
// | current_timestamp_created_1 | Employee.created_date | Manage Employee Records | CreateEmployeeRecord | create_employee_record_1  | INPUT | Employee.created_date |
// | current_timestamp_updated_1 | Employee.updated_date | Manage Employee Records | CreateEmployeeRecord | create_employee_record_1  | INPUT | Employee.updated_date |

// Execution pathway:
// Default: route to ViewEmployeeDatabase

//  Role System Permissions:

// Tenant: "FinanceFlow Bank"

// | role_id | permission_id | granted_actions | row_level_conditions | natural_language | version | status |
// |---------|---------------|-----------------|---------------------|------------------|---------|--------|
// | ROLE-HR_MANAGER | PERM_CREATE_EMPLOYEE | ["create", "read"] | {"access_level": "all_records"} | HR Manager can create and read all employee records | 1 | active |
// ''');
    // return buffer.toString();
    // Header - LO Name
    buffer.writeln(localObjective.name ?? "Local Objective");
    // buffer.writeln();

    // Core LO Metadata
    _writeCoreMetadata(buffer, localObjective, globalObjectives, tenantId);
    // Inputs section (includes attribute sources)
    _writeInputs(buffer, localObjective);

    // Outputs section
    _writeOutputs(buffer, localObjective);

    // Validation Stack
    _writeValidationStack(
        buffer, localObjective, validationRules, dataConstraints);

    // UI Stack
    _writeUIStack(buffer, localObjective);

    // Nested Function Stack
    _writeNestedFunctionStack(buffer, localObjective);

    // Input Mapping
    _writeInputMapping(buffer, localObjective);

    // Nested Function Mapping
    _writeNestedFunctionMapping(buffer, localObjective, globalObjectives);

    // Execution Pathway
    _writeExecutionPathway(buffer, localObjective);

    // Role System Permissions
    _writeRoleSystemPermissions(buffer, globalObjectives, tenantId);

    return buffer.toString();
  }

  /// Convert all LocalObjectives from GoModel
  static Map<String, String> convertAllLocalObjectives(GoModel goModel) {
    Map<String, String> convertedLOs = {};

    if (goModel.localObjectivesList != null) {
      for (var lo in goModel.localObjectivesList!) {
        String loText = convertLocalObjectiveToText(
            lo,
            goModel.globalObjectives,
            goModel.dataConstraints,
            goModel.validationRules?.rules,
            't001');
        convertedLOs[lo.name ?? 'LO${lo.loNumber}'] = loText;
      }
    }

    return convertedLOs;
  }

  static void _writeCoreMetadata(StringBuffer buffer, LocalObjectivesList lo,
      GlobalObjectives? globalObjectives, String? tenantId) {
    buffer.writeln();
    buffer.writeln('name: "${_cleanString(lo.name ?? "")}"');
    buffer.writeln('version: "${_cleanString(lo.version ?? "1.0")}"');
    buffer.writeln('status: "${_cleanString(lo.status ?? "Active")}"');
    buffer.writeln(
        'workflow_source: "${_cleanString(lo.workflowSource ?? lo.workSource ?? "origin")}"');
    buffer.writeln('function_type: "${_determineFunctionType(lo.name ?? "")}"');
    buffer.writeln('agent_type: "${_cleanString(lo.agentType ?? "HUMAN")}"');
    buffer.writeln('ui_type: "${_cleanString(lo.uiType ?? "Form")}"');
    buffer.writeln(
        'execution_rights: "${_cleanString(lo.executionRights ?? "User")}"');
    buffer.writeln(
        'Tenant: "${tenantId ?? _cleanString(globalObjectives?.tenantName ?? lo.tenantName ?? "")}"');
    buffer.writeln();
  }

  static void _writeInputs(StringBuffer buffer, LocalObjectivesList lo) {
    buffer.writeln('Inputs: ${_generateInputsFromEntities(lo)}');
    buffer.writeln();
    // Write detailed attribute information immediately after inputs
    _writeAttributeSources(buffer, lo);
  }

  static void _writeAttributeSources(
      StringBuffer buffer, LocalObjectivesList lo) {
    if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
      for (var entity in lo.entitiesList!) {
        if (entity.attributes != null) {
          for (var attribute in entity.attributes!) {
            String entityName = _cleanString(entity.name ?? "");
            String attributeName = _convertToCase(attribute.name ?? '');

            if (entityName.isNotEmpty && attributeName.isNotEmpty) {
              buffer.writeln('$entityName.$attributeName');
              buffer.writeln(
                  '    - Source: ${_determineAttributeSource(attribute)}');
              buffer.writeln(
                  '    - Agent: ${_determineAttributeAgent(attribute)}');
              if (attribute.dataSource == 'Nested Function') {
                buffer.writeln(
                    '    - Function Reference: ${_generateFunctionReference(attributeName, entityName)}');
              }
              buffer.writeln(
                  '    - Attribute type: ${_determineAttributeType(attribute)}');
              buffer.writeln();
            }
          }
        }
      }
    }
    // No fallback - if no entities, write nothing
  }

  static void _writeOutputs(StringBuffer buffer, LocalObjectivesList lo) {
    buffer.writeln('Outputs: ${_generateOutputsFromEntities(lo)}');
    buffer.writeln();
  }

  static void _writeValidationStack(StringBuffer buffer, LocalObjectivesList lo,
      List<Rule>? rules, List<DataConstraint>? constraints) {
    buffer.writeln('Validation Stack');
    buffer.writeln();

    if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
      for (var entity in lo.entitiesList!) {
        if (entity.attributes != null) {
          for (var attribute in entity.attributes!) {
            String entityName = _cleanString(entity.name ?? "");
            String attributeName = _convertToCase(attribute.name ?? '');

            if (entityName.isNotEmpty && attributeName.isNotEmpty) {
              _writeAttributeValidation(buffer, entity, attribute, constraints);
            }
          }
        }
      }
    }
    // No fallback - if no entities, write nothing
  }

  static void _writeUIStack(StringBuffer buffer, LocalObjectivesList lo) {
    buffer.writeln('UI Stack');
    buffer.writeln();

    if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
      for (var entity in lo.entitiesList!) {
        if (entity.attributes != null) {
          for (var attribute in entity.attributes!) {
            String entityName = _cleanString(entity.name ?? "");
            String attributeName = _convertToCase(attribute.name ?? '');

            if (entityName.isNotEmpty && attributeName.isNotEmpty) {
              _writeAttributeUI(buffer, entity, attribute);
            }
          }
        }
      }
    }
    // No fallback - if no entities, write nothing
  }

  static void _writeNestedFunctionStack(
      StringBuffer buffer, LocalObjectivesList lo) {
    buffer.writeln('Nested Function Stack:');
    buffer.writeln();

    if ((lo.entitiesList ?? [])
        .where(
          (element) => (element.attributes ?? [])
              .where(
                (attribute) => attribute.dataSource == 'Nested Function',
              )
              .isNotEmpty,
        )
        .isNotEmpty) {
      // Always write at least one nested function for system-generated attributes
      bool hasNestedFunctions = false;

      if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
        for (var entity in lo.entitiesList!) {
          if (entity.attributes != null) {
            String entityName = _cleanString(entity.name ?? "");

            for (var attribute in entity.attributes!) {
              String attributeName = _convertToCase(attribute.name ?? '');

              if (entityName.isNotEmpty &&
                      attributeName.isNotEmpty &&
                      attribute.dataSource == 'Nested Function'
                  //  &&
                  // _isSystemGenerated(attribute)
                  ) {
                String functionName =
                    _generateFunctionReference(attributeName, entityName);
                buffer.writeln('Function: $functionName');
                buffer.writeln(
                    '    - Source Attribute: ${attribute.nestedFunctionAttributeName}');
                buffer.writeln(
                    '    - Function Name: ${attribute.nestedFunctionName ?? _getFunctionName(attributeName)}');
                buffer
                    .writeln('    - Inputs: ${attribute.nestedFunctionInputs}');
                buffer.writeln(
                    '    - Conditions: ${attribute.nestedFunctionCondition}');
                buffer.writeln('    - Outputs: [$entityName.$attributeName]');
                buffer.writeln();
                hasNestedFunctions = true;
              }
            }
          }
        }
      }

      // // If no system-generated attributes found, create a default nested function
      // if (!hasNestedFunctions &&
      //     lo.entitiesList != null &&
      //     lo.entitiesList!.isNotEmpty) {
      //   var firstEntity = lo.entitiesList!.first;
      //   String entityName = _cleanString(firstEntity.name ?? "");
      //   if (firstEntity.attributes != null &&
      //       firstEntity.attributes!.isNotEmpty) {
      //     var firstAttribute = firstEntity.attributes!.first;
      //     String attributeName = _convertToCase(firstAttribute.name ?? '');
      //     String functionName =
      //         _generateFunctionReference(attributeName, entityName);

      //     buffer.writeln('Function: $functionName');
      //     buffer.writeln('    - Source Attribute: $entityName.$attributeName');
      //     buffer.writeln('    - Function Name: current_timestamp');
      //     buffer.writeln('    - Inputs: []');
      //     buffer.writeln('    - Conditions: []');
      //     buffer.writeln('    - Outputs: [$entityName.$attributeName]');
      //     buffer.writeln();
      //   }
      // }
    }
  }

  static void _writeInputMapping(StringBuffer buffer, LocalObjectivesList lo) {
    buffer.writeln('Input Mapping:');

    if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
      for (var entity in lo.entitiesList!) {
        if (entity.attributes != null) {
          String entityName = _cleanString(entity.name ?? "");

          for (var attribute in entity.attributes!) {
            String attributeName = _convertToCase(attribute.name ?? '');

            if (entityName.isNotEmpty &&
                attributeName.isNotEmpty &&
                !_isSystemGenerated(attribute)) {
              buffer.writeln(
                  '- $entityName.$attributeName -> $entityName.$attributeName');
            }
          }
        }
      }
    }
    buffer.writeln();
  }

  static void _writeNestedFunctionMapping(StringBuffer buffer,
      LocalObjectivesList lo, GlobalObjectives? globalObjectives) {
    buffer.writeln('Nested Function Mapping');
    buffer.writeln();

    if ((lo.entitiesList ?? [])
        .where(
          (element) => (element.attributes ?? [])
              .where(
                (attribute) => attribute.dataSource == 'Nested Function',
              )
              .isNotEmpty,
        )
        .isNotEmpty) {
      buffer.writeln(
          '| Function Name | Source Item | Target GO Name | Target LO Name | Target Nested Function Name | Target Stack | Target Item |');
      buffer.writeln(
          '|---------------|-------------|----------------|----------------|---------------------------|-------------|-------------|');

      String goName = globalObjectives?.name ?? "test";
      String loName = lo.name ?? "";
      bool hasMappings = false;

      if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
        for (var entity in lo.entitiesList!) {
          if (entity.attributes != null) {
            String entityName = _cleanString(entity.name ?? "");

            for (var attribute in entity.attributes!) {
              if (attribute.dataSource == 'Nested Function') {
                String attributeName = _convertToCase(attribute.name ?? '');

                if (entityName.isNotEmpty && attributeName.isNotEmpty) {
                  String functionName =
                      _generateFunctionReference(attributeName, entityName);
                  String targetNestedFunctionName = loName.isNotEmpty
                      ? '${_camelCaseToSnakeCase(loName)}_1'
                      : 'test1_1';
                  buffer.writeln(
                      '| $functionName | ${attribute.nestedFunctionInputs} | $goName | $loName | $targetNestedFunctionName| INPUT | $entityName.$attributeName |');
                  hasMappings = true;
                }
              }
            }
          }
        }
      }
      // // If no mappings were written, add at least one default mapping
      // if (!hasMappings) {
      //   buffer.writeln(
      //       '| generate_customer_id_1 | Customer.customerId | $goName | $loName | test1_1| INPUT | Customer.customerId |');
      // }
    }

    buffer.writeln();
  }

  static void _writeExecutionPathway(
      StringBuffer buffer, LocalObjectivesList lo) {
    buffer.writeln('Execution pathway:');

    if (lo.pathwayData?.sequentialData?.selectedLO != null) {
      buffer.writeln(
          'Default: route to ${lo.pathwayData!.sequentialData!.selectedLO}');
    } else if (lo.terminal == true) {
      buffer.writeln('Default: Complete process');
    } else {
      // Try to determine next LO based on naming convention
      String nextLO = _determineNextLO(lo.name ?? "");
      buffer.writeln('Default: route to $nextLO');
    }
    buffer.writeln();
  }

  static void _writeRoleSystemPermissions(StringBuffer buffer,
      GlobalObjectives? globalObjectives, String? tenantId) {
    buffer.writeln('Role System Permissions:');
    buffer.writeln();
    buffer
        .writeln('Tenant: "${tenantId ?? globalObjectives?.tenantName ?? ""}"');
    buffer.writeln();

    buffer.writeln(
        '| role_id | permission_id | granted_actions | row_level_conditions | natural_language | version | status |');
    buffer.writeln(
        '|---------|---------------|-----------------|---------------------|------------------|---------|--------|');
    buffer.writeln(
        '| ROLE-HR_MANAGER | PERM_CREATE_EMPLOYEE | ["create", "read"] | {"access_level": "all_records"} | HR Manager can create and read all employee records | 1 | active |');
  }

  // Helper methods
  static String _cleanString(String input) {
    return input.replaceAll('\\n', '').replaceAll('\\t', '').trim();
  }

  static String _determineFunctionType(String loName) {
    if (loName.toLowerCase().contains('create')) return 'Create';
    if (loName.toLowerCase().contains('update')) return 'Update';
    if (loName.toLowerCase().contains('delete')) return 'Delete';
    if (loName.toLowerCase().contains('view') ||
        loName.toLowerCase().contains('details')) {
      return 'Read';
    }
    return 'Process';
  }

  static String _generateFunctionReference(String attr, [String? entityName]) {
    // Convert attribute name to snake_case for function reference
    String snakeAttr = _camelCaseToSnakeCase(attr);
    String entityPrefix = entityName != null
        ? _camelCaseToSnakeCase(entityName).toLowerCase()
        : '';

    // Handle specific attribute patterns
    switch (snakeAttr.toLowerCase()) {
      case 'customer_id':
      case 'customerid':
        return 'generate_customer_id_1';
      case 'first_name':
      case 'firstname':
        return 'generate_customer_name_1';
      case 'last_name':
      case 'lastname':
        return 'generate_customer_name_2';
      case 'email':
      case 'customer_email':
      case 'customeremail':
        return 'generate_customer_email_1';
      case 'customer_name':
      case 'customername':
        return 'generate_customer_name_1';
      case 'customer_score':
      case 'customerscore':
        return 'generate_customer_score_1';
      case 'employee_id':
        return 'generate_employee_id_1';
      case 'entry_date':
        return 'current_timestamp_entry_1';
      case 'created_at':
      case 'created_date':
        return 'current_timestamp_created_1';
      case 'updated_at':
      case 'updated_date':
        return 'current_timestamp_updated_1';
      default:
        // Generate function reference based on entity and attribute
        // Avoid double entity prefix if attribute already contains entity name
        if (entityPrefix.isNotEmpty &&
            !snakeAttr.toLowerCase().startsWith(entityPrefix)) {
          return 'generate_${entityPrefix}_${snakeAttr}_1';
        }
        return 'generate_${snakeAttr}_1';
    }
  }

  static String _determineAttributeSource(ObjectAttribute attribute) {
    // if (_isSystemGenerated(attribute)) {
    //   return 'nested_function';
    // }
    return _convertToSnakeCase(attribute.dataSource ?? 'User');
  }

  static String _determineAttributeAgent(ObjectAttribute attribute) {
    // if (_isSystemGenerated(attribute)) {
    //   return 'DIGITAL';
    // }
    return attribute.dataSource == "User" ? 'HUMAN' : "DIGITAL";
  }

  static String _determineAttributeType(ObjectAttribute attribute) {
    // if (_isSystemGenerated(attribute)) {
    //   return 'system_generated';
    // }
    switch (attribute.dataSource) {
      case 'User':
        return 'input';
      case 'Nested Function':
        return 'system_generated';
      case 'mapping':
        return 'system_generated';
      case 'constant':
        return 'system_generated';
      case 'condition potential':
        return 'system_generated';
    }
    return 'input';
  }

  static bool _isSystemGenerated(ObjectAttribute attribute) {
    // Check if attribute is system-generated based on uiControl or other properties
    String attrName = attribute.name?.toLowerCase() ?? '';

    return attribute.uiControl == 'label' ||
        attrName.contains('id') ||
        attrName.contains('date') ||
        attrName.contains('_at') ||
        attrName.contains('created') ||
        attrName.contains('updated') ||
        attrName.contains('timestamp');
  }

  static String _generateInputsFromEntities(LocalObjectivesList lo) {
    String entityName = '';
    List<String> attrs = [];

    if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
      for (var entity in lo.entitiesList!) {
        if (entity.attributes != null && entity.attributes!.isNotEmpty) {
          entityName = _cleanString(entity.name ?? '');
          for (var attribute in entity.attributes!) {
            String attributeName = _convertToCase(attribute.name ?? '');
            if (attributeName.isNotEmpty) {
              // No timestamp annotations - just add the attribute name
              attrs.add(attributeName);
            }
          }
        }
      }
    }

    // If no entities or attributes found, return a default format to avoid empty inputs
    if (entityName.isEmpty || attrs.isEmpty) {
      return 'Entity with attributes';
    }

    return '$entityName with ${attrs.join(', ')}';
  }

  static String _generateOutputsFromEntities(LocalObjectivesList lo) {
    return _generateInputsFromEntities(lo); // Usually same as inputs
  }

  static DataConstraint? _findConstraintForAttribute(
      List<DataConstraint>? constraints, String entity, String attribute) {
    if (constraints == null) return null;

    return constraints.firstWhere(
      (c) =>
          c.entity?.toLowerCase() == entity.toLowerCase() &&
          c.attribute?.toLowerCase() == attribute.toLowerCase(),
      orElse: () => DataConstraint(),
    );
  }

  static String _getValidationFunction(
      String attr, DataConstraint? constraint) {
    if (constraint?.constraintText != null) {
      return constraint!.constraintText!;
    }
    // Provide basic validation based on attribute characteristics
    if (attr.toLowerCase().contains('email')) {
      return 'validate_email_format';
    } else if (attr.toLowerCase().contains('id')) {
      return 'validate_required_field';
    } else {
      return 'validate_required_field';
    }
  }

  static String _getSuccessValue(String attr) {
    return 'true';
  }

  static String _getFailureValue(String attr) {
    return 'false';
  }

  static String _getSuccessMessage(String attr) {
    // Provide basic success message based on attribute name
    String cleanAttr = attr.replaceAll('_', ' ').toLowerCase();
    return '$cleanAttr validation passed';
  }

  static String _getFailureMessage(String attr, DataConstraint? constraint) {
    if (constraint?.errorMessage != null) {
      return constraint!.errorMessage!;
    }
    // Provide basic failure message based on attribute characteristics
    String cleanAttr = attr.replaceAll('_', ' ').toLowerCase();
    if (attr.toLowerCase().contains('email')) {
      return '$cleanAttr must be a valid email address';
    } else {
      return '$cleanAttr is required';
    }
  }

  static String _getUIControl(ObjectAttribute attribute) {
    String uiControl = attribute.uiControl ?? '';
    String attrName = attribute.name?.toLowerCase() ?? '';

    // Check if this is a system-generated field first
    if (_isSystemGenerated(attribute)) {
      return 'label';
    }

    // If uiControl is empty or "String", determine appropriate control based on attribute name
    if (uiControl.isEmpty || uiControl == 'String') {
      if (attrName.contains('email')) {
        return 'email';
      } else if (attrName.contains('phone') ||
          attrName.contains('mobile') ||
          attrName.contains('contact')) {
        return 'mobile_no';
      } else if (attrName.contains('date')) {
        return 'date';
      } else if (attrName.contains('address')) {
        return 'multiline';
      } else {
        return 'text';
      }
    }

    return uiControl;
  }

  static String _getUIDataType(ObjectAttribute attribute) {
    String dataType = attribute.dataType ?? '';

    // If dataType is empty or "String", determine appropriate type based on attribute name
    if (dataType.isEmpty || dataType == 'String') {
      String attrName = attribute.name?.toLowerCase() ?? '';

      if (attrName.contains('date')) {
        return attrName.contains('time') ? 'datetime' : 'date';
      } else {
        return 'text';
      }
    }

    return dataType;
  }

  static bool _isEditable(ObjectAttribute attribute) {
    // If uiControl is 'label', it's not editable
    return attribute.uiControl != 'label';
  }

  static bool _isRequired(ObjectAttribute attribute) {
    return attribute.required ?? false;
  }

  static bool _isHidden(ObjectAttribute attribute) {
    // No hidden property available, return false
    return false;
  }

  static String _getFunctionName(String attr) {
    switch (attr) {
      case 'employee_id':
      case 'id':
        return 'generate_id';
      default:
        return 'current_timestamp';
    }
  }

  static String _getFunctionInputs(String attr) {
    switch (attr) {
      case 'employee_id':
        return "[prefix='EMP', year='YYYY', month='MM', sequence='sequential']";
      case 'id':
        return "[prefix='ID', sequence='sequential']";
      default:
        return '[]';
    }
  }

  static String _camelCaseToSnakeCase(String input) {
    if (input.isEmpty) return input;

    // Handle cases where input is already snake_case or has underscores
    if (input.contains('_')) return input.toLowerCase();

    // Convert camelCase to snake_case
    String result = input.replaceAllMapped(
        RegExp(r'([A-Z])'), (match) => '_${match.group(1)!.toLowerCase()}');

    // Remove leading underscore if it exists
    if (result.startsWith('_')) {
      result = result.substring(1);
    }

    return result.toLowerCase();
  }

  static String _determineNextLO(String currentLO) {
    if (currentLO.contains('Create')) {
      return '${currentLO.replaceAll('Create', 'View')}Details';
    } else if (currentLO.contains('View')) {
      return currentLO.replaceAll('View', 'Update');
    }
    return 'NextLocalObjective';
  }

  static void _writeAttributeValidation(
      StringBuffer buffer,
      ObjectCreationModel entity,
      ObjectAttribute attribute,
      List<DataConstraint>? constraints) {
    // Implementation for entity-based validation
    // This would extract validation info from the attribute object
    String entityName = _cleanString(entity.name ?? "Entity");
    String attributeName = _convertToCase(attribute.name ?? '');

    // Find constraint for this attribute
    DataConstraint? constraint =
        _findConstraintForAttribute(constraints, entityName, attributeName);

    buffer.writeln('$entityName.$attributeName');
    buffer.writeln('- Attribute: $entityName.$attributeName');
    buffer.writeln(
        '- Validation Function: ${_getValidationFunction(attributeName, constraint)}');
    buffer.writeln('- Success Value: ${_getSuccessValue(attributeName)}');
    buffer.writeln('- Failure Value: ${_getFailureValue(attributeName)}');
    buffer.writeln('- Success Message: "${_getSuccessMessage(attributeName)}"');
    buffer.writeln(
        '- Failure Message: "${_getFailureMessage(attributeName, constraint)}"');
    buffer.writeln();
  }

  static void _writeAttributeUI(StringBuffer buffer, ObjectCreationModel entity,
      ObjectAttribute attribute) {
    // Implementation for entity-based UI
    // This would extract UI info from the attribute object
    String entityName = _cleanString(entity.name ?? "Entity");
    String attributeName = _convertToCase(attribute.name ?? '');

    buffer.writeln('$entityName.$attributeName');
    buffer.writeln('- UI Control: ${_getUIControl(attribute)}');
    buffer.writeln('- Data Type: ${_getUIDataType(attribute)}');
    buffer.writeln('- Editable: ${_isEditable(attribute)}');
    buffer.writeln('- Required: ${_isRequired(attribute)}');
    buffer.writeln('- Hidden: ${_isHidden(attribute)}');
    buffer.writeln();
  }

  /// Save LocalObjective text to file
  static Future<void> saveToFile(String loText, String filePath) async {
    final file = File(filePath);
    await file.writeAsString(loText);
    if (kDebugMode) {
      print('LocalObjective converted and saved to: $filePath');
    }
  }

  static String _convertToCase(String s) {
    String temp = '';
    final list = s.split(" ");
    for (var element in list) {
      if (list.first == element) {
        temp += element.toLowerCase();
      } else {
        temp += element[0].toUpperCase() + element.substring(1).toLowerCase();
      }
    }
    return temp;
  }

  static String _convertToSnakeCase(String s) {
    String temp = '';
    final list = s.split(" ");
    for (var element in list) {
      if (list.last == element) {
        temp += element.toLowerCase();
      } else {
        temp += '${element.toLowerCase()}_';
      }
    }
    return temp;
  }
}

// Usage example
void main() async {
  // Create a sample LocalObjective
  LocalObjectivesList createEmployeeLO = LocalObjectivesList(
    loNumber: 1,
    name: 'CreateEmployeeRecord',
    version: '1.1',
    status: 'Active',
    workSource: 'origin',
    functionType: 'Create',
    agentType: 'HUMAN',
    uiType: 'Form',
    executionRights: 'HR Manager',
    tenantName: 'FinanceFlow Bank',
    terminal: false,
  );

  GlobalObjectives globalObjectives = GlobalObjectives(
    name: 'Manage Employee Records',
    tenantName: 'FinanceFlow Bank',
  );

  // Convert to text
  String loText = LocalObjectiveToTextConverter.convertLocalObjectiveToText(
      createEmployeeLO,
      globalObjectives,
      null, // dataConstraints
      null, // validationRules
      't001');

  if (kDebugMode) {
    print(loText);
  }

  // Save to file
  await LocalObjectiveToTextConverter.saveToFile(
      loText, 'create_employee_lo.txt');
}
