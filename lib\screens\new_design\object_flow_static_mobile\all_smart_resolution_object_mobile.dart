import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/create_object_mobile.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/extract_details_object_mobile.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/industry_bundles_object_mobile.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';

class AllSmartResolutionObjectMobile extends StatefulWidget {
  const AllSmartResolutionObjectMobile({super.key});

  @override
  State<AllSmartResolutionObjectMobile> createState() =>
      _AllSmartResolutionObjectMobileState();
}

class _AllSmartResolutionObjectMobileState
    extends State<AllSmartResolutionObjectMobile> {
  bool _isAIEnabled = true;
  bool _showCloseIcon = true; // Show close icon when screen opens
  bool _showCloseIconIndustry = false; // Show industry icon initially
  // JSON data for the validation rules
  final Map<String, dynamic> validationData = {
    "header": {
      "title": "All Smart Resolution",
      "bulkApplyButton": "Bulk Apply"
    },
    "validationRules": [
      {
        "id": 1,
        "title": "Email Validation Rules",
        "description":
            "Add IS_VALID_EMAIL and IS_UNIQUE operators for email field",
        "status": "SIMPLE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 2,
        "title": "Phone Format Validation",
        "description":
            "Apply MATCHES_PATTERN operator for international phone numbers",
        "status": "MODERATE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 3,
        "title": "Customer-Address Relationship",
        "description":
            "Configure one-to-many with CASCADE delete for address cleanup",
        "status": "MODERATE",
        "actionType": "Attribute",
        "hasCircleIcon": false
      },
      {
        "id": 4,
        "title": "Title of the Issue",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      },
      {
        "id": 5,
        "title": "Address Auto-Complete",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      }
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: const CustomDrawer(),
      backgroundColor: const Color(0xFFFfffff),
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildValidationRulesList(),
                ),
              ],
            ),
            // Bottom action buttons positioned on top
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomActions(context),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFFF7F9FB),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0058FF), Color(0xFF0B3A91)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E5E5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            'assets/images/object-flow-mobile/sparkle-stars-ai.svg', // Update with your SVG asset path
            width: 28,
            height: 28,
          ),
          const SizedBox(width: 8),
          Text(
            validationData['header']['title'],
            style: FontManager.getCustomStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              // Add bulk apply functionality here
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
              ),
              child: Text(
                validationData['header']['bulkApplyButton'],
                style: FontManager.getCustomStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF000000),
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationRulesList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        itemCount: validationData['validationRules'].length,
        itemBuilder: (context, index) {
          final rule = validationData['validationRules'][index];
          return _buildValidationRuleItem(
            rule['title'],
            rule['description'],
            rule['actionType'],
            rule['status'],
          );
        },
      ),
    );
  }

  Widget _buildValidationRuleItem(
    String title,
    String description,
    String actionType,
    String status,
  ) {
    return GestureDetector(
      onTap: () {
        // Add validation rule item tap functionality here
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: const Color(0xFFAFAFAF),
            width: .5,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              // Left blue accent bar
              Container(
                width: 5,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF0058FF), Color(0xFF0B3A91)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(11),
                    bottomLeft: Radius.circular(11),
                  ),
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        title,
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Description
                      Text(
                        description,
                        style: FontManager.getCustomStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF000000),
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                      const SizedBox(height: 2),
                      // Action type and status row
                      Row(
                        children: [
                          // Status badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getComplexityColor(status),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w300,
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                          ),

                          const SizedBox(
                            width: 4,
                          ),

                          Text(
                            actionType,
                            style: FontManager.getCustomStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w300,
                              color: const Color(0xFF666666),
                              fontFamily: FontManager.fontFamilyInter,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getComplexityColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFFE8F5E8);
      case 'MODERATE':
        return const Color(0xFFFFF4E6);
      case 'COMPLEX':
        return const Color(0xFFFFE6E6);
      default:
        return const Color(0xFFF5F5F5);
    }
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Left side icons in one container - stacked vertically
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const CreateObjectScreenMobile(),
                  // ...existing code...
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const begin = Offset(1.0, 0.0); // Slide from right
                    const end = Offset.zero;
                    const curve = Curves.easeInOut;
                    final tween = Tween(begin: begin, end: end)
                        .chain(CurveTween(curve: curve));
                    return SlideTransition(
                      position: animation.drive(tween),
                      child: child,
                    );
                  },
// ...existing code...
                  transitionDuration: const Duration(milliseconds: 600),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Color(0xAAD0D0D0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    offset: const Offset(0, 3), // X: 0, Y: 3
                    blurRadius: 20, // Blur: 20
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.menu,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(height: 8),
                  Icon(
                    Icons.mic_none,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                ],
              ),
            ),
          ),

          const Spacer(),

          // Right side colored circles - stacked vertically
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  // Since we're already on the smart resolution screen and showing close icon, just close
                  Navigator.pop(context);
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF064CD1), Color(0xFF0093FF)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.black,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Center(
                    child: _showCloseIcon
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          )
                        : SvgPicture.asset(
                            'assets/images/object-flow-mobile/sparkle-stars-ai.svg', // Update with your SVG asset path
                            width: 30,
                            height: 30,
                          ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  if (_showCloseIconIndustry) {
                    Navigator.pop(context);
                  } else {
                    setState(() {
                      _showCloseIconIndustry = true;
                    });
                    Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  IndustryBundlesObjectMobile(),
                          transitionDuration: Duration(milliseconds: 100),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) =>
                                  FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        )).then((_) {
                      // Reset the icon when returning from the screen
                      setState(() {
                        _showCloseIconIndustry = false;
                      });
                    });
                  }
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFF673AB7),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ], // Purple color
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Center(
                    child: _showCloseIconIndustry
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          )
                        : SvgPicture.asset(
                            'assets/images/object-flow-mobile/industry-bundles.svg', // Update with your SVG asset path
                            width: 30,
                            height: 30,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
