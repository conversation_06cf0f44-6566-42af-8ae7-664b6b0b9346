// To parse this JSON data, do
//
//     final saveValidDepartmentModel = saveValidDepartmentModelFromJson(jsonString);

import 'dart:convert';

SaveValidDepartmentModel saveValidDepartmentModelFromJson(String str) => SaveValidDepartmentModel.fromJson(json.decode(str));

String saveValidDepartmentModelToJson(SaveValidDepartmentModel data) => json.encode(data.toJson());

class SaveValidDepartmentModel {
    bool? success;
    SavedData? savedData;
    DeptValidationResult? validationResult;
    List<dynamic>? dependencyErrors;
    DeptUniquenessResult? uniquenessResult;
    String? operation;
    Issues? issues;
    bool? hasErrors;
    bool? hasWarnings;
    IssueCounts? issueCounts;

    SaveValidDepartmentModel({
        this.success,
        this.savedData,
        this.validationResult,
        this.dependencyErrors,
        this.uniquenessResult,
        this.operation,
        this.issues,
        this.hasErrors,
        this.hasWarnings,
        this.issueCounts,
    });

    factory SaveValidDepartmentModel.fromJson(Map<String, dynamic> json) => SaveValidDepartmentModel(
        success: json["success"],
        savedData: json["saved_data"] == null ? null : SavedData.fromJson(json["saved_data"]),
        validationResult: json["validation_result"] == null ? null : DeptValidationResult.fromJson(json["validation_result"]),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<dynamic>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessResult: json["uniqueness_result"] == null ? null : DeptUniquenessResult.fromJson(json["uniqueness_result"]),
        operation: json["operation"],
        issues: json["issues"] == null ? null : Issues.fromJson(json["issues"]),
        hasErrors: json["has_errors"],
        hasWarnings: json["has_warnings"],
        issueCounts: json["issue_counts"] == null ? null : IssueCounts.fromJson(json["issue_counts"]),
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "saved_data": savedData?.toJson(),
        "validation_result": validationResult?.toJson(),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_result": uniquenessResult?.toJson(),
        "operation": operation,
        "issues": issues?.toJson(),
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "issue_counts": issueCounts?.toJson(),
    };
}

class IssueCounts {
    int? totalErrors;
    int? totalWarnings;
    int? totalExceptions;
    int? validationErrors;
    int? dependencyErrors;
    int? uniquenessIssues;
    int? parsingIssues;
    int? mongoErrors;
    int? postgresErrors;
    bool? hasCriticalErrors;
    bool? hasWarnings;

    IssueCounts({
        this.totalErrors,
        this.totalWarnings,
        this.totalExceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
        this.hasCriticalErrors,
        this.hasWarnings,
    });

    factory IssueCounts.fromJson(Map<String, dynamic> json) => IssueCounts(
        totalErrors: json["total_errors"],
        totalWarnings: json["total_warnings"],
        totalExceptions: json["total_exceptions"],
        validationErrors: json["validation_errors"],
        dependencyErrors: json["dependency_errors"],
        uniquenessIssues: json["uniqueness_issues"],
        parsingIssues: json["parsing_issues"],
        mongoErrors: json["mongo_errors"],
        postgresErrors: json["postgres_errors"],
        hasCriticalErrors: json["has_critical_errors"],
        hasWarnings: json["has_warnings"],
    );

    Map<String, dynamic> toJson() => {
        "total_errors": totalErrors,
        "total_warnings": totalWarnings,
        "total_exceptions": totalExceptions,
        "validation_errors": validationErrors,
        "dependency_errors": dependencyErrors,
        "uniqueness_issues": uniquenessIssues,
        "parsing_issues": parsingIssues,
        "mongo_errors": mongoErrors,
        "postgres_errors": postgresErrors,
        "has_critical_errors": hasCriticalErrors,
        "has_warnings": hasWarnings,
    };
}

class Issues {
    IssueCounts? summary;
    List<dynamic>? errors;
    List<dynamic>? warnings;
    List<dynamic>? exceptions;
    List<dynamic>? validationErrors;
    List<dynamic>? dependencyErrors;
    List<dynamic>? uniquenessIssues;
    List<dynamic>? parsingIssues;
    List<dynamic>? mongoErrors;
    List<dynamic>? postgresErrors;

    Issues({
        this.summary,
        this.errors,
        this.warnings,
        this.exceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
    });

    factory Issues.fromJson(Map<String, dynamic> json) => Issues(
        summary: json["summary"] == null ? null : IssueCounts.fromJson(json["summary"]),
        errors: json["errors"] == null ? [] : List<dynamic>.from(json["errors"]!.map((x) => x)),
        warnings: json["warnings"] == null ? [] : List<dynamic>.from(json["warnings"]!.map((x) => x)),
        exceptions: json["exceptions"] == null ? [] : List<dynamic>.from(json["exceptions"]!.map((x) => x)),
        validationErrors: json["validation_errors"] == null ? [] : List<dynamic>.from(json["validation_errors"]!.map((x) => x)),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<dynamic>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessIssues: json["uniqueness_issues"] == null ? [] : List<dynamic>.from(json["uniqueness_issues"]!.map((x) => x)),
        parsingIssues: json["parsing_issues"] == null ? [] : List<dynamic>.from(json["parsing_issues"]!.map((x) => x)),
        mongoErrors: json["mongo_errors"] == null ? [] : List<dynamic>.from(json["mongo_errors"]!.map((x) => x)),
        postgresErrors: json["postgres_errors"] == null ? [] : List<dynamic>.from(json["postgres_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "summary": summary?.toJson(),
        "errors": errors == null ? [] : List<dynamic>.from(errors!.map((x) => x)),
        "warnings": warnings == null ? [] : List<dynamic>.from(warnings!.map((x) => x)),
        "exceptions": exceptions == null ? [] : List<dynamic>.from(exceptions!.map((x) => x)),
        "validation_errors": validationErrors == null ? [] : List<dynamic>.from(validationErrors!.map((x) => x)),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_issues": uniquenessIssues == null ? [] : List<dynamic>.from(uniquenessIssues!.map((x) => x)),
        "parsing_issues": parsingIssues == null ? [] : List<dynamic>.from(parsingIssues!.map((x) => x)),
        "mongo_errors": mongoErrors == null ? [] : List<dynamic>.from(mongoErrors!.map((x) => x)),
        "postgres_errors": postgresErrors == null ? [] : List<dynamic>.from(postgresErrors!.map((x) => x)),
    };
}

class SavedData {
    int? savedDataId;
    String? tenantId;
    String? name;
    String? description;
    dynamic departmentHeadRoleId;
    dynamic parentDepartmentId;
    String? naturalLanguage;
    DateTime? createdAt;
    dynamic createdBy;
    DateTime? updatedAt;
    dynamic updatedBy;
    int? version;
    String? status;
    String? id;

    SavedData({
        this.savedDataId,
        this.tenantId,
        this.name,
        this.description,
        this.departmentHeadRoleId,
        this.parentDepartmentId,
        this.naturalLanguage,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
        this.version,
        this.status,
        this.id,
    });

    factory SavedData.fromJson(Map<String, dynamic> json) => SavedData(
        savedDataId: json["id"],
        tenantId: json["tenant_id"],
        name: json["name"],
        description: json["description"],
        departmentHeadRoleId: json["department_head_role_id"],
        parentDepartmentId: json["parent_department_id"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
        version: json["version"],
        status: json["status"],
        id: json["_id"],
    );

    Map<String, dynamic> toJson() => {
        "id": savedDataId,
        "tenant_id": tenantId,
        "name": name,
        "description": description,
        "department_head_role_id": departmentHeadRoleId,
        "parent_department_id": parentDepartmentId,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
        "version": version,
        "status": status,
        "_id": id,
    };
}

class DeptUniquenessResult {
    bool? isUnique;
    String? status;
    String? message;

    DeptUniquenessResult({
        this.isUnique,
        this.status,
        this.message,
    });

    factory DeptUniquenessResult.fromJson(Map<String, dynamic> json) => DeptUniquenessResult(
        isUnique: json["is_unique"],
        status: json["status"],
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "is_unique": isUnique,
        "status": status,
        "message": message,
    };
}

class DeptValidationResult {
    List<dynamic>? structureErrors;
    List<dynamic>? requiredFieldErrors;
    List<dynamic>? dataTypeErrors;
    List<dynamic>? customErrors;

    DeptValidationResult({
        this.structureErrors,
        this.requiredFieldErrors,
        this.dataTypeErrors,
        this.customErrors,
    });

    factory DeptValidationResult.fromJson(Map<String, dynamic> json) => DeptValidationResult(
        structureErrors: json["structure_errors"] == null ? [] : List<dynamic>.from(json["structure_errors"]!.map((x) => x)),
        requiredFieldErrors: json["required_field_errors"] == null ? [] : List<dynamic>.from(json["required_field_errors"]!.map((x) => x)),
        dataTypeErrors: json["data_type_errors"] == null ? [] : List<dynamic>.from(json["data_type_errors"]!.map((x) => x)),
        customErrors: json["custom_errors"] == null ? [] : List<dynamic>.from(json["custom_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "structure_errors": structureErrors == null ? [] : List<dynamic>.from(structureErrors!.map((x) => x)),
        "required_field_errors": requiredFieldErrors == null ? [] : List<dynamic>.from(requiredFieldErrors!.map((x) => x)),
        "data_type_errors": dataTypeErrors == null ? [] : List<dynamic>.from(dataTypeErrors!.map((x) => x)),
        "custom_errors": customErrors == null ? [] : List<dynamic>.from(customErrors!.map((x) => x)),
    };
}
