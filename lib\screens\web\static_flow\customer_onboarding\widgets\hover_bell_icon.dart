import 'package:flutter/material.dart';

/// Hover bell icon widget for expansion tiles
class HoverBellIcon extends StatefulWidget {
  final bool isExpanded;
  final VoidCallback? onTap;
  final Color? color;
  final double size;

  const HoverBellIcon({
    super.key,
    required this.isExpanded,
    this.onTap,
    this.color,
    this.size = 24.0,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(HoverBellIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: _isHovered ? Colors.grey[100] : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value *
                    3.14159, // 180 degrees in radians
                child: Icon(
                  Icons.notifications_outlined,
                  size: widget.size,
                  color: widget.color ??
                      (_isHovered ? Colors.blue[600] : Colors.grey[600]),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
