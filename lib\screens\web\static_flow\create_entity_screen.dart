import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/static_flow/ai_object_right_panel.dart';
import 'package:nsl/screens/web/static_flow/inner_entity_screen.dart';
import 'package:nsl/screens/web/static_flow/web_left_panel_solution.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'package:nsl/providers/creation_provider.dart';
import 'package:nsl/screens/web/static_flow/role_creation_screen.dart';

// void main() {
//   runApp(const MaterialApp(home: AiObjectScreenStatic()));
// }

/// Constants for the AI Object Screen Static layout
/// Constants for the AI Object Screen Static layout
class _LayoutConstants {
  static const double middleColumnWidthNormal =
      0.50; // 70% when right panel is shown (since left column is removed)
  static const double rightColumnWidthNormal = 0.30;
  static const double leftColumnWidthNormal = 0.20;
  // 30% when right panel is shown (since left column is removed)
  static const double middleColumnWidthExpanded =
      1.0; // 100% when right panel is hidden

  static const double gripWidth = 12.0;
  static const double gripHeight = 80.0;
  static const double gripLineWidth = 6.0;
  static const double gripLineHeight = 1.5;
  static const int gripLineCount = 3;
}

/// Colors used in the AI Object Screen Static
class _ScreenColors {
  static const Color middlePanelBackground = Color(0xFFF7F9FB);
  static const Color borderColor = AppColors.greyBorder;
  static const Color gripLineColor = Color(0xFF666666);
  static const Color hoverBackground = Color(0xFFE4EDFF);
  static const Color primaryBlue = AppColors.primaryBlue;
}

class CreateEntityScreen extends StatefulWidget {
  const CreateEntityScreen({super.key});

  @override
  State<CreateEntityScreen> createState() => _CreateEntityScreenStaticState();
}

class _CreateEntityScreenStaticState extends State<CreateEntityScreen> {
  bool _isRightPanelVisible = false;
  double _middleColumnWidth = _LayoutConstants.middleColumnWidthNormal;

  @override
  void initState() {
    super.initState();
    _updateColumnWidths();
  }

  /// Updates column widths based on right panel visibility
  void _updateColumnWidths() {
    if (_isRightPanelVisible) {
      _middleColumnWidth = _LayoutConstants.middleColumnWidthNormal;
    } else {
      _middleColumnWidth = _LayoutConstants.middleColumnWidthExpanded;
    }
  }

  /// Toggles the right panel visibility
  void _toggleRightPanel() {
    setState(() {
      _isRightPanelVisible = !_isRightPanelVisible;
      _updateColumnWidths();
    });
  }

  /// Shows the delete object confirmation dialog matching the design
  void _showDeleteObjectConfirmation(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 549,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with warning icon and close button
                      Container(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 10, top: 10),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Warning icon
                            Container(
                              width: 24,
                              height: 24,
                              decoration: const BoxDecoration(
                                color: Color(0xFFFFFFFF),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.warning_amber_rounded,
                                color: Color(0xFFFF2019),
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 12),
                            // Title
                            const Expanded(
                              child: Text(
                                'Attention',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            // Close button
                            IconButton(
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 20,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Main message
                      Padding(
                        padding: const EdgeInsets.only(left: 26, right: 26),
                        child: Center(
                          child: Text(
                            '"If you go back now, your document will not be saved. Do you want to continue editing and save your work instead?"',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                              height: 1.4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(height: 26),

                      // Options text
                      Center(
                        child: Text(
                          'Options:',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Option 1
                      Center(
                        child: Text(
                          'Continue Editing & Save',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),

                      // Option 2
                      Center(
                        child: Text(
                          'Back Leave Without Saving',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Action buttons
                      Container(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 10, top: 10),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Back button
                            TextButton(
                              onPressed: () {
                                Navigator.of(dialogContext)
                                    .pop(); // Close the dialog first
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.createObjectScreenStatic;
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  side: BorderSide(color: Colors.grey[300]!),
                                ),
                              ),
                              child: const Text(
                                'Back',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),

                            // Continue button
                            ElevatedButton(
                              onPressed: () {
                                Navigator.of(dialogContext)
                                    .pop(); // Close the dialog
                                // Continue editing - stay on current screen
                                // Provider.of<WebHomeProvider>(context,
                                //           listen: false)
                                //       .currentScreenIndex =
                                //   ScreenConstants.createObjectScreenStatic;
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    const Color(0xFF007AFF), // Blue color
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'Continue',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildMainContent(),
        ],
      ),
    );
  }

  /// Builds the header section with navigation and toggle button
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.md,
      ),
      child: Row(
        children: [
          // Back arrow on the left side
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TextButton.icon(
                  onPressed: () {
                    _showDeleteObjectConfirmation(context);
                  },
                  icon: const Icon(Icons.arrow_back, color: Colors.black),
                  label:
                      const Text('Back', style: TextStyle(color: Colors.black)),
                  style: ButtonStyle(
                    padding:
                        WidgetStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                    overlayColor:
                        WidgetStateProperty.all<Color>(Colors.transparent),
                    minimumSize: WidgetStateProperty.all(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    alignment: Alignment.centerLeft,
                  ),
                ),
              ],
            ),
          ),
          // Centered HoverNavItems
          // const CustomTabHeader(),
          // Spacer and toggle button at the right end
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _HoverExpandButton(
                  isExpanded: _isRightPanelVisible,
                  onTap: _toggleRightPanel,
                  tooltipMessage: _isRightPanelVisible
                      ? 'Expand middle panel'
                      : 'Show right panel',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the main content with 2-column layout
  Widget _buildMainContent() {
    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Consumer<WebHomeProviderStatic>(
            builder: (context, value, child) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: constraints.maxWidth *
                        _LayoutConstants.leftColumnWidthNormal,
                    child: WebLeftPanelSolution(),
                  ),
                  // Middle column takes up the remaining space
                  Expanded(
                    child: _buildMiddleColumn(constraints),
                  ),

                  // Only render right column when panel is visible
                  if (_isRightPanelVisible) _buildRightColumn(constraints),
                ],
              );
            },
          );
        },
      ),
    );
  }

  /// Builds the middle column
  Widget _buildMiddleColumn(BoxConstraints constraints) {
    return SizedBox(
      width: constraints.maxWidth * _middleColumnWidth,
      child: _buildMiddlePanelContent(),
    );
  }

  /// Builds the main middle panel content
  Widget _buildMiddlePanelContent() {
    return Container(
      decoration: const BoxDecoration(
        color: _ScreenColors.middlePanelBackground,
        // border: Border(
        //   left: BorderSide(color: _ScreenColors.borderColor, width: 1),
        //   right: BorderSide(color: _ScreenColors.borderColor, width: 1),
        // ),
      ),
      child: Padding(
        padding: EdgeInsets.only(left: 10, right: 8),
        child: Consumer<CreationProvider>(
          builder: (context, creationProvider, child) {
            // Switch between different middle content based on provider state
            switch (creationProvider.currentMiddleScreen) {
              case 0: // Role creation screen
                return RoleCreationScreen();
              case 1: // Entity creation screen (default)
              default:
                return InnerEntityScreen();
            }
          },
        ),
      ),
    );
  }

  /// Builds the right column (AI Object Right Panel)
  Widget _buildRightColumn(BoxConstraints constraints) {
    return SizedBox(
      width: constraints.maxWidth * _LayoutConstants.rightColumnWidthNormal,
      child: Padding(
        padding: EdgeInsets.only(left: 8, right: 10),
        child: const AiObjectRightPanel(),
      ),
    );
  }
}

/// Expand/collapse button with hover effect for panel toggle
class _HoverExpandButton extends StatefulWidget {
  final bool isExpanded;
  final VoidCallback onTap;
  final String tooltipMessage;

  const _HoverExpandButton({
    required this.isExpanded,
    required this.onTap,
    this.tooltipMessage = '',
  });

  @override
  State<_HoverExpandButton> createState() => _HoverExpandButtonState();
}

class _HoverExpandButtonState extends State<_HoverExpandButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        message: widget.tooltipMessage,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: AppSpacing.lg,
            height: AppSpacing.lg,
            decoration: BoxDecoration(
              color: _isHovered
                  ? _ScreenColors.hoverBackground
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
            ),
            child: Center(
              child: Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..scale(widget.isExpanded ? 1.0 : -1.0, 1.0, 1.0),
                child: Icon(
                  Icons.login,
                  size: AppSpacing.size20,
                  color: _isHovered
                      ? _ScreenColors.primaryBlue
                      : Colors.grey.shade600,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
