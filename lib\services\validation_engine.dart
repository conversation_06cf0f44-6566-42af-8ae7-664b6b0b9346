import 'dart:math';

/// Validation Engine Service - Handles all validation logic
class ValidationEngine {
  
  /// Main validation method that processes validation rules
  static ValidationResult validateField(String value, Map<String, dynamic> rule) {
    try {
      final function = rule['validationFunction'] as String? ?? '';
      final successCondition = rule['successCondition'] as String? ?? 'true';
      final failureCondition = rule['failureCondition'] as String? ?? 'false';
      final successMessage = rule['successMessage'] as String? ?? 'Valid input';
      final failureMessage = rule['failureMessage'] as String? ?? 'Invalid input';
      
      // Step 1: Apply validation function
      bool functionResult = _applyValidationFunction(function, value);
      
      // Step 2: Evaluate conditions
      bool finalResult;
      if (functionResult) {
        finalResult = ConditionEvaluator.evaluateCondition(successCondition, value, rule);
      } else {
        finalResult = !ConditionEvaluator.evaluateCondition(failureCondition, value, rule);
      }
      
      return ValidationResult(
        isValid: finalResult,
        message: finalResult ? successMessage : failureMessage,
        rule: rule,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'Validation error: ${e.toString()}',
        rule: rule,
      );
    }
  }
  
  /// Apply specific validation functions
  static bool _applyValidationFunction(String function, String value) {
    switch (function.toLowerCase()) {
      case 'validate_email_format':
        return _validateEmail(value);
      case 'required_field':
        return _validateRequired(value);
      case 'validate_phone_format':
        return _validatePhone(value);
      case 'validate_number':
        return _validateNumber(value);
      case 'validate_url':
        return _validateUrl(value);
      case 'validate_date':
        return _validateDate(value);
      case 'range_check':
        return _validateRange(value);
      case 'length_check':
        return _validateLength(value);
      case 'pattern_match':
        return _validatePattern(value);
      case 'no_validation':
      case '':
        return true;
      default:
        print('Unknown validation function: $function');
        return true; // Default to valid for unknown functions
    }
  }
  
  /// Email validation
  static bool _validateEmail(String value) {
    if (value.isEmpty) return false;
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value);
  }
  
  /// Required field validation
  static bool _validateRequired(String value) {
    return value.trim().isNotEmpty;
  }
  
  /// Phone number validation
  static bool _validatePhone(String value) {
    if (value.isEmpty) return false;
    // Remove all non-digit characters for validation
    String digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');
    return digitsOnly.length >= 10 && digitsOnly.length <= 15;
  }
  
  /// Number validation
  static bool _validateNumber(String value) {
    if (value.isEmpty) return false;
    return double.tryParse(value) != null;
  }
  
  /// URL validation
  static bool _validateUrl(String value) {
    if (value.isEmpty) return false;
    try {
      Uri.parse(value);
      return value.startsWith('http://') || value.startsWith('https://');
    } catch (e) {
      return false;
    }
  }
  
  /// Date validation (supports multiple formats)
  static bool _validateDate(String value) {
    if (value.isEmpty) return false;
    try {
      DateTime.parse(value);
      return true;
    } catch (e) {
      // Try other common date formats
      final formats = [
        RegExp(r'^\d{2}/\d{2}/\d{4}$'), // MM/dd/yyyy
        RegExp(r'^\d{2}-\d{2}-\d{4}$'), // MM-dd-yyyy
        RegExp(r'^\d{4}-\d{2}-\d{2}$'), // yyyy-MM-dd
      ];
      
      for (final format in formats) {
        if (format.hasMatch(value)) return true;
      }
      return false;
    }
  }
  
  /// Range validation (for numbers)
  static bool _validateRange(String value) {
    final number = double.tryParse(value);
    return number != null && number >= 0 && number <= 999999;
  }
  
  /// Length validation
  static bool _validateLength(String value) {
    return value.length >= 1 && value.length <= 255;
  }
  
  /// Pattern matching validation
  static bool _validatePattern(String value) {
    // Basic alphanumeric pattern
    return RegExp(r'^[a-zA-Z0-9\s]+$').hasMatch(value);
  }
  
  /// Validate multiple fields at once
  static Map<String, ValidationResult> validateMultipleFields(
    Map<String, String> fieldValues,
    List<Map<String, dynamic>> validationRules,
  ) {
    Map<String, ValidationResult> results = {};
    
    for (final entry in fieldValues.entries) {
      final fieldName = entry.key;
      final fieldValue = entry.value;
      
      // Find validation rules for this field
      final fieldRules = validationRules.where(
        (rule) => rule['attribute'] == fieldName,
      ).toList();
      
      if (fieldRules.isNotEmpty) {
        // Apply first matching rule (you can modify to apply all rules)
        results[fieldName] = validateField(fieldValue, fieldRules.first);
      } else {
        // No validation rules - consider valid
        results[fieldName] = ValidationResult(
          isValid: true,
          message: 'No validation required',
          rule: {},
        );
      }
    }
    
    return results;
  }
  
  /// Check if all validations passed
  static bool isFormValid(Map<String, ValidationResult> validationResults) {
    return validationResults.values.every((result) => result.isValid);
  }
}

/// Dynamic Condition Evaluator
class ConditionEvaluator {
  
  /// Evaluate dynamic conditions like "age >= 18", "phone LENGTH == 10"
  static bool evaluateCondition(String condition, String value, Map<String, dynamic> context) {
    try {
      // Handle simple boolean conditions
      if (condition.toLowerCase() == 'true') return true;
      if (condition.toLowerCase() == 'false') return false;
      
      // Handle LENGTH conditions
      if (condition.contains('LENGTH')) {
        return _evaluateLengthCondition(condition, value);
      }
      
      // Handle numeric comparisons
      if (condition.contains('>=') || condition.contains('<=') || 
          condition.contains('>') || condition.contains('<') || 
          condition.contains('==') || condition.contains('!=')) {
        return _evaluateNumericCondition(condition, value);
      }
      
      // Handle NULL checks
      if (condition.contains('IS_NULL')) {
        return value.isEmpty;
      }
      
      if (condition.contains('IS_NOT_NULL')) {
        return value.isNotEmpty;
      }
      
      // Handle CONTAINS checks
      if (condition.contains('CONTAINS')) {
        return _evaluateContainsCondition(condition, value);
      }
      
      // Default to true for unknown conditions
      return true;
    } catch (e) {
      print('Error evaluating condition "$condition": $e');
      return false;
    }
  }
  
  /// Evaluate length-based conditions
  static bool _evaluateLengthCondition(String condition, String value) {
    try {
      // Examples: "LENGTH == 10", "LENGTH >= 5", "phone LENGTH == 10"
      final parts = condition.split('LENGTH');
      if (parts.length != 2) return false;
      
      final operatorPart = parts[1].trim();
      final length = value.length;
      
      if (operatorPart.startsWith('==')) {
        final expectedLength = int.tryParse(operatorPart.substring(2).trim()) ?? 0;
        return length == expectedLength;
      } else if (operatorPart.startsWith('>=')) {
        final minLength = int.tryParse(operatorPart.substring(2).trim()) ?? 0;
        return length >= minLength;
      } else if (operatorPart.startsWith('<=')) {
        final maxLength = int.tryParse(operatorPart.substring(2).trim()) ?? 0;
        return length <= maxLength;
      } else if (operatorPart.startsWith('>')) {
        final minLength = int.tryParse(operatorPart.substring(1).trim()) ?? 0;
        return length > minLength;
      } else if (operatorPart.startsWith('<')) {
        final maxLength = int.tryParse(operatorPart.substring(1).trim()) ?? 0;
        return length < maxLength;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Evaluate numeric conditions
  static bool _evaluateNumericCondition(String condition, String value) {
    try {
      final numValue = double.tryParse(value);
      if (numValue == null) return false;
      
      // Extract operator and expected value
      String operator = '';
      String expectedValueStr = '';
      
      if (condition.contains('>=')) {
        final parts = condition.split('>=');
        operator = '>=';
        expectedValueStr = parts[1].trim();
      } else if (condition.contains('<=')) {
        final parts = condition.split('<=');
        operator = '<=';
        expectedValueStr = parts[1].trim();
      } else if (condition.contains('==')) {
        final parts = condition.split('==');
        operator = '==';
        expectedValueStr = parts[1].trim();
      } else if (condition.contains('!=')) {
        final parts = condition.split('!=');
        operator = '!=';
        expectedValueStr = parts[1].trim();
      } else if (condition.contains('>')) {
        final parts = condition.split('>');
        operator = '>';
        expectedValueStr = parts[1].trim();
      } else if (condition.contains('<')) {
        final parts = condition.split('<');
        operator = '<';
        expectedValueStr = parts[1].trim();
      }
      
      final expectedValue = double.tryParse(expectedValueStr);
      if (expectedValue == null) return false;
      
      switch (operator) {
        case '>=': return numValue >= expectedValue;
        case '<=': return numValue <= expectedValue;
        case '==': return numValue == expectedValue;
        case '!=': return numValue != expectedValue;
        case '>': return numValue > expectedValue;
        case '<': return numValue < expectedValue;
        default: return false;
      }
    } catch (e) {
      return false;
    }
  }
  
  /// Evaluate CONTAINS conditions
  static bool _evaluateContainsCondition(String condition, String value) {
    try {
      // Example: "CONTAINS @gmail.com"
      final parts = condition.split('CONTAINS');
      if (parts.length != 2) return false;
      
      final searchTerm = parts[1].trim();
      return value.toLowerCase().contains(searchTerm.toLowerCase());
    } catch (e) {
      return false;
    }
  }
}

/// Validation Result Model
class ValidationResult {
  final bool isValid;
  final String message;
  final Map<String, dynamic> rule;
  
  const ValidationResult({
    required this.isValid,
    required this.message,
    required this.rule,
  });
  
  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, message: $message)';
  }
}
