// To parse this JSON data, do
//
//     final getEntityEnumValueModel = getEntityEnumValueModelFromJson(jsonString);

import 'dart:convert';

GetEntityEnumValueModel getEntityEnumValueModelFromJson(String str) =>
    GetEntityEnumValueModel.fromJson(json.decode(str));

String getEntityEnumValueModelToJson(GetEntityEnumValueModel data) =>
    json.encode(data.toJson());

class GetEntityEnumValueModel {
  bool? success;
  String? entityId;
  List<MongoDraftEnum>? postgresEnumValues;
  List<MongoDraftEnum>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;
  String? operation;

  GetEntityEnumValueModel({
    this.success,
    this.entityId,
    this.postgresEnumValues,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
    this.operation,
  });

  GetEntityEnumValueModel copyWith({
    bool? success,
    String? entityId,
    List<MongoDraftEnum>? postgresEnumValues,
    List<MongoDraftEnum>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
    String? operation,
  }) =>
      GetEntityEnumValueModel(
        success: success ?? this.success,
        entityId: entityId ?? this.entityId,
        postgresEnumValues: postgresEnumValues ?? this.postgresEnumValues,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
        operation: operation ?? this.operation,
      );

  factory GetEntityEnumValueModel.fromJson(Map<String, dynamic> json) =>
      GetEntityEnumValueModel(
        success: json["success"],
        entityId: json["entity_id"],
        postgresEnumValues: json["postgres_enum_values"] == null
            ? []
            : List<MongoDraftEnum>.from(json["postgres_enum_values"]!
                .map((x) => MongoDraftEnum.fromJson(x))),
        mongoDrafts: json["mongo_drafts"] == null
            ? []
            : List<MongoDraftEnum>.from(
                json["mongo_drafts"]!.map((x) => MongoDraftEnum.fromJson(x))),
        totalPostgres: json["total_postgres"],
        totalDrafts: json["total_drafts"],
        operation: json["operation"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "entity_id": entityId,
        "postgres_enum_values": postgresEnumValues == null
            ? []
            : List<dynamic>.from(postgresEnumValues!.map((x) => x)),
        "mongo_drafts": mongoDrafts == null
            ? []
            : List<dynamic>.from(mongoDrafts!.map((x) => x.toJson())),
        "total_postgres": totalPostgres,
        "total_drafts": totalDrafts,
        "operation": operation,
      };
}

class MongoDraftEnum {
  String? id;
  String? enumValueId;
  String? attributeId;
  String? value;
  String? displayName;
  String? description;
  int? sortOrder;
  bool? isActive;
  String? naturalLanguage;
  String? version;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? enumValueStatus;
  List<dynamic>? changesDetected;
  String? attributeName;

  MongoDraftEnum({
    this.id,
    this.enumValueId,
    this.attributeId,
    this.value,
    this.displayName,
    this.description,
    this.sortOrder,
    this.isActive,
    this.naturalLanguage,
    this.version,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.enumValueStatus,
    this.changesDetected,
    this.attributeName,
  });

  MongoDraftEnum copyWith({
    String? id,
    String? enumValueId,
    String? attributeId,
    String? value,
    String? displayName,
    String? description,
    int? sortOrder,
    bool? isActive,
    String? naturalLanguage,
    String? version,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? enumValueStatus,
    List<dynamic>? changesDetected,
    String? attributeName,
  }) =>
      MongoDraftEnum(
        id: id ?? this.id,
        enumValueId: enumValueId ?? this.enumValueId,
        attributeId: attributeId ?? this.attributeId,
        value: value ?? this.value,
        displayName: displayName ?? this.displayName,
        description: description ?? this.description,
        sortOrder: sortOrder ?? this.sortOrder,
        isActive: isActive ?? this.isActive,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        enumValueStatus: enumValueStatus ?? this.enumValueStatus,
        changesDetected: changesDetected ?? this.changesDetected,
        attributeName: attributeName ?? this.attributeName,
      );

  factory MongoDraftEnum.fromJson(Map<String, dynamic> json) => MongoDraftEnum(
        id: json["_id"],
        enumValueId: json["enum_value_id"],
        attributeId: json["attribute_id"],
        value: json["value"],
        displayName: json["display_name"],
        description: json["description"],
        sortOrder: json["sort_order"],
        isActive: json["is_active"],
        naturalLanguage: json["natural_language"],
        version: json["version"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        enumValueStatus: json["enum_value_status"],
        changesDetected: json["changes_detected"] == null
            ? []
            : List<dynamic>.from(json["changes_detected"]!.map((x) => x)),
        attributeName: json["attribute_name"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "enum_value_id": enumValueId,
        "attribute_id": attributeId,
        "value": value,
        "display_name": displayName,
        "description": description,
        "sort_order": sortOrder,
        "is_active": isActive,
        "natural_language": naturalLanguage,
        "version": version,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "enum_value_status": enumValueStatus,
        "changes_detected": changesDetected == null
            ? []
            : List<dynamic>.from(changesDetected!.map((x) => x)),
        "attribute_name": attributeName,
      };
}
