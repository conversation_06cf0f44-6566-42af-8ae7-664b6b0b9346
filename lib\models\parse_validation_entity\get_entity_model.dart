// To parse this JSON data, do
//
//     final getEntityModel = getEntityModelFromJson(jsonString);

import 'dart:convert';

GetEntityModel getEntityModelFromJson(String str) =>
    GetEntityModel.fromJson(json.decode(str));

String getEntityModelToJson(GetEntityModel data) => json.encode(data.toJson());

class GetEntityModel {
  bool? success;
  String? entityId;
  MongoDraft? postgresRecord;
  MongoDraft? mongoDraft;
  bool? foundInPostgres;
  bool? foundInMongo;
  String? operation;

  GetEntityModel({
    this.success,
    this.entityId,
    this.postgresRecord,
    this.mongoDraft,
    this.foundInPostgres,
    this.foundInMongo,
    this.operation,
  });

  GetEntityModel copyWith({
    bool? success,
    String? entityId,
    MongoDraft? postgresRecord,
    MongoDraft? mongoDraft,
    bool? foundInPostgres,
    bool? foundInMongo,
    String? operation,
  }) =>
      GetEntityModel(
        success: success ?? this.success,
        entityId: entityId ?? this.entityId,
        postgresRecord: postgresRecord ?? this.postgresRecord,
        mongoDraft: mongoDraft ?? this.mongoDraft,
        foundInPostgres: foundInPostgres ?? this.foundInPostgres,
        foundInMongo: foundInMongo ?? this.foundInMongo,
        operation: operation ?? this.operation,
      );

  factory GetEntityModel.fromJson(Map<String, dynamic> json) => GetEntityModel(
        success: json["success"],
        entityId: json["entity_id"],
        postgresRecord: json["postgres_record"] == null
            ? null
            : MongoDraft.fromJson(json["postgres_record"]),
        mongoDraft: json["mongo_draft"] == null
            ? null
            : MongoDraft.fromJson(json["mongo_draft"]),
        foundInPostgres: json["found_in_postgres"],
        foundInMongo: json["found_in_mongo"],
        operation: json["operation"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "entity_id": entityId,
        "postgres_record": postgresRecord?.toJson(),
        "mongo_draft": mongoDraft?.toJson(),
        "found_in_postgres": foundInPostgres,
        "found_in_mongo": foundInMongo,
        "operation": operation,
      };
}

class MongoDraft {
  String? id;
  String? entityId;
  String? name;
  String? displayName;
  String? tenantId;
  String? tenantName;
  String? businessDomain;
  String? category;
  List<String>? tags;
  String? archivalStrategy;
  String? icon;
  String? colourTheme;
  int? version;
  String? status;
  String? type;
  String? description;
  String? tableName;
  String? naturalLanguage;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? entityStatus;
  List<dynamic>? changesDetected;
  String? iconType;
  String? iconContent;

  MongoDraft({
    this.id,
    this.entityId,
    this.name,
    this.displayName,
    this.tenantId,
    this.tenantName,
    this.businessDomain,
    this.category,
    this.tags,
    this.archivalStrategy,
    this.icon,
    this.colourTheme,
    this.version,
    this.status,
    this.type,
    this.description,
    this.tableName,
    this.naturalLanguage,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.entityStatus,
    this.changesDetected,
    this.iconType,
    this.iconContent,
  });

  MongoDraft copyWith({
    String? id,
    String? entityId,
    String? name,
    String? displayName,
    String? tenantId,
    String? tenantName,
    String? businessDomain,
    String? category,
    List<String>? tags,
    String? archivalStrategy,
    String? icon,
    String? colourTheme,
    int? version,
    String? status,
    String? type,
    String? description,
    String? tableName,
    String? naturalLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? entityStatus,
    List<dynamic>? changesDetected,
    String? iconType,
    String? iconContent,
  }) =>
      MongoDraft(
        id: id ?? this.id,
        entityId: entityId ?? this.entityId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        tenantId: tenantId ?? this.tenantId,
        tenantName: tenantName ?? this.tenantName,
        businessDomain: businessDomain ?? this.businessDomain,
        category: category ?? this.category,
        tags: tags ?? this.tags,
        archivalStrategy: archivalStrategy ?? this.archivalStrategy,
        icon: icon ?? this.icon,
        colourTheme: colourTheme ?? this.colourTheme,
        version: version ?? this.version,
        status: status ?? this.status,
        type: type ?? this.type,
        description: description ?? this.description,
        tableName: tableName ?? this.tableName,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        entityStatus: entityStatus ?? this.entityStatus,
        changesDetected: changesDetected ?? this.changesDetected,
        iconType: iconType ?? this.iconType,
        iconContent: iconContent ?? this.iconContent,
      );

  factory MongoDraft.fromJson(Map<String, dynamic> json) => MongoDraft(
        id: json["_id"],
        entityId: json["entity_id"],
        name: json["name"],
        displayName: json["display_name"],
        tenantId: json["tenant_id"],
        tenantName: json["tenant_name"],
        businessDomain: json["business_domain"],
        category: json["category"],
        tags: json["tags"] == null
            ? []
            : List<String>.from(json["tags"]!.map((x) => x)),
        archivalStrategy: json["archival_strategy"],
        icon: json["icon"],
        colourTheme: json["colour_theme"],
        version: json["version"],
        status: json["status"],
        type: json["type"],
        description: json["description"],
        tableName: json["table_name"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        entityStatus: json["entity_status"],
        changesDetected: json["changes_detected"] == null
            ? []
            : List<dynamic>.from(json["changes_detected"]!.map((x) => x)),
        iconType: json["icon_type"],
        iconContent: json["icon_content"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "entity_id": entityId,
        "name": name,
        "display_name": displayName,
        "tenant_id": tenantId,
        "tenant_name": tenantName,
        "business_domain": businessDomain,
        "category": category,
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "archival_strategy": archivalStrategy,
        "icon": icon,
        "colour_theme": colourTheme,
        "version": version,
        "status": status,
        "type": type,
        "description": description,
        "table_name": tableName,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "entity_status": entityStatus,
        "changes_detected": changesDetected == null
            ? []
            : List<dynamic>.from(changesDetected!.map((x) => x)),
        "icon_type": iconType,
        "icon_content": iconContent,
      };
}
