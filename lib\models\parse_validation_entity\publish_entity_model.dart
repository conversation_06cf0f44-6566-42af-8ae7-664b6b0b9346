// To parse this JSON data, do
//
//     final publishEntityModel = publishEntityModelFromJson(jsonString);

import 'dart:convert';

PublishEntityModel publishEntityModelFromJson(String str) =>
    PublishEntityModel.fromJson(json.decode(str));

String publishEntityModelToJson(PublishEntityModel data) =>
    json.encode(data.toJson());

class PublishEntityModel {
  String? status;
  String? message;
  String? entityId;
  String? tenantId;
  DeploymentSummary? deploymentSummary;
  DetailedResults? detailedResults;
  List<String>? sequenceOrder;
  String? operation;

  PublishEntityModel({
    this.status,
    this.message,
    this.entityId,
    this.tenantId,
    this.deploymentSummary,
    this.detailedResults,
    this.sequenceOrder,
    this.operation,
  });

  PublishEntityModel copyWith({
    String? status,
    String? message,
    String? entityId,
    String? tenantId,
    DeploymentSummary? deploymentSummary,
    DetailedResults? detailedResults,
    List<String>? sequenceOrder,
    String? operation,
  }) =>
      PublishEntityModel(
        status: status ?? this.status,
        message: message ?? this.message,
        entityId: entityId ?? this.entityId,
        tenantId: tenantId ?? this.tenantId,
        deploymentSummary: deploymentSummary ?? this.deploymentSummary,
        detailedResults: detailedResults ?? this.detailedResults,
        sequenceOrder: sequenceOrder ?? this.sequenceOrder,
        operation: operation ?? this.operation,
      );

  factory PublishEntityModel.fromJson(Map<String, dynamic> json) =>
      PublishEntityModel(
        status: json["status"],
        message: json["message"],
        entityId: json["entity_id"],
        tenantId: json["tenant_id"],
        deploymentSummary: json["deployment_summary"] == null
            ? null
            : DeploymentSummary.fromJson(json["deployment_summary"]),
        detailedResults: json["detailed_results"] == null
            ? null
            : DetailedResults.fromJson(json["detailed_results"]),
        sequenceOrder: json["sequence_order"] == null
            ? []
            : List<String>.from(json["sequence_order"]!.map((x) => x)),
        operation: json["operation"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "entity_id": entityId,
        "tenant_id": tenantId,
        "deployment_summary": deploymentSummary?.toJson(),
        "detailed_results": detailedResults?.toJson(),
        "sequence_order": sequenceOrder == null
            ? []
            : List<dynamic>.from(sequenceOrder!.map((x) => x)),
        "operation": operation,
      };
}

class DeploymentSummary {
  String? entityId;
  String? tenantId;
  String? schema;
  List<String>? sequenceCompleted;
  List<dynamic>? sequenceFailed;
  int? totalDeployed;
  int? totalFailed;

  DeploymentSummary({
    this.entityId,
    this.tenantId,
    this.schema,
    this.sequenceCompleted,
    this.sequenceFailed,
    this.totalDeployed,
    this.totalFailed,
  });

  DeploymentSummary copyWith({
    String? entityId,
    String? tenantId,
    String? schema,
    List<String>? sequenceCompleted,
    List<dynamic>? sequenceFailed,
    int? totalDeployed,
    int? totalFailed,
  }) =>
      DeploymentSummary(
        entityId: entityId ?? this.entityId,
        tenantId: tenantId ?? this.tenantId,
        schema: schema ?? this.schema,
        sequenceCompleted: sequenceCompleted ?? this.sequenceCompleted,
        sequenceFailed: sequenceFailed ?? this.sequenceFailed,
        totalDeployed: totalDeployed ?? this.totalDeployed,
        totalFailed: totalFailed ?? this.totalFailed,
      );

  factory DeploymentSummary.fromJson(Map<String, dynamic> json) =>
      DeploymentSummary(
        entityId: json["entity_id"],
        tenantId: json["tenant_id"],
        schema: json["schema"],
        sequenceCompleted: json["sequence_completed"] == null
            ? []
            : List<String>.from(json["sequence_completed"]!.map((x) => x)),
        sequenceFailed: json["sequence_failed"] == null
            ? []
            : List<dynamic>.from(json["sequence_failed"]!.map((x) => x)),
        totalDeployed: json["total_deployed"],
        totalFailed: json["total_failed"],
      );

  Map<String, dynamic> toJson() => {
        "entity_id": entityId,
        "tenant_id": tenantId,
        "schema": schema,
        "sequence_completed": sequenceCompleted == null
            ? []
            : List<dynamic>.from(sequenceCompleted!.map((x) => x)),
        "sequence_failed": sequenceFailed == null
            ? []
            : List<dynamic>.from(sequenceFailed!.map((x) => x)),
        "total_deployed": totalDeployed,
        "total_failed": totalFailed,
      };
}

class DetailedResults {
  AttributeValidations? entities;
  AttributeValidations? entityAttributes;
  AttributeValidations? attributeValidations;
  AttributeValidations? entityRelationships;

  DetailedResults({
    this.entities,
    this.entityAttributes,
    this.attributeValidations,
    this.entityRelationships,
  });

  DetailedResults copyWith({
    AttributeValidations? entities,
    AttributeValidations? entityAttributes,
    AttributeValidations? attributeValidations,
    AttributeValidations? entityRelationships,
  }) =>
      DetailedResults(
        entities: entities ?? this.entities,
        entityAttributes: entityAttributes ?? this.entityAttributes,
        attributeValidations: attributeValidations ?? this.attributeValidations,
        entityRelationships: entityRelationships ?? this.entityRelationships,
      );

  factory DetailedResults.fromJson(Map<String, dynamic> json) =>
      DetailedResults(
        entities: json["entities"] == null
            ? null
            : AttributeValidations.fromJson(json["entities"]),
        entityAttributes: json["entity_attributes"] == null
            ? null
            : AttributeValidations.fromJson(json["entity_attributes"]),
        attributeValidations: json["attribute_validations"] == null
            ? null
            : AttributeValidations.fromJson(json["attribute_validations"]),
        entityRelationships: json["entity_relationships"] == null
            ? null
            : AttributeValidations.fromJson(json["entity_relationships"]),
      );

  Map<String, dynamic> toJson() => {
        "entities": entities?.toJson(),
        "entity_attributes": entityAttributes?.toJson(),
        "attribute_validations": attributeValidations?.toJson(),
        "entity_relationships": entityRelationships?.toJson(),
      };
}

class AttributeValidations {
  int? successfulInserts;
  int? failedInserts;
  int? totalProcessed;
  List<Detail>? details;

  AttributeValidations({
    this.successfulInserts,
    this.failedInserts,
    this.totalProcessed,
    this.details,
  });

  AttributeValidations copyWith({
    int? successfulInserts,
    int? failedInserts,
    int? totalProcessed,
    List<Detail>? details,
  }) =>
      AttributeValidations(
        successfulInserts: successfulInserts ?? this.successfulInserts,
        failedInserts: failedInserts ?? this.failedInserts,
        totalProcessed: totalProcessed ?? this.totalProcessed,
        details: details ?? this.details,
      );

  factory AttributeValidations.fromJson(Map<String, dynamic> json) =>
      AttributeValidations(
        successfulInserts: json["successful_inserts"],
        failedInserts: json["failed_inserts"],
        totalProcessed: json["total_processed"],
        details: json["details"] == null
            ? []
            : List<Detail>.from(
                json["details"]!.map((x) => Detail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "successful_inserts": successfulInserts,
        "failed_inserts": failedInserts,
        "total_processed": totalProcessed,
        "details": details == null
            ? []
            : List<dynamic>.from(details!.map((x) => x.toJson())),
      };
}

class Detail {
  bool? success;
  String? insertedId;
  String? schema;
  String? entityId;
  String? name;
  int? attributesInserted;
  String? originalEntityId;
  String? attributeId;
  String? originalAttributeId;

  Detail({
    this.success,
    this.insertedId,
    this.schema,
    this.entityId,
    this.name,
    this.attributesInserted,
    this.originalEntityId,
    this.attributeId,
    this.originalAttributeId,
  });

  Detail copyWith({
    bool? success,
    String? insertedId,
    String? schema,
    String? entityId,
    String? name,
    int? attributesInserted,
    String? originalEntityId,
    String? attributeId,
    String? originalAttributeId,
  }) =>
      Detail(
        success: success ?? this.success,
        insertedId: insertedId ?? this.insertedId,
        schema: schema ?? this.schema,
        entityId: entityId ?? this.entityId,
        name: name ?? this.name,
        attributesInserted: attributesInserted ?? this.attributesInserted,
        originalEntityId: originalEntityId ?? this.originalEntityId,
        attributeId: attributeId ?? this.attributeId,
        originalAttributeId: originalAttributeId ?? this.originalAttributeId,
      );

  factory Detail.fromJson(Map<String, dynamic> json) => Detail(
        success: json["success"],
        insertedId: json["inserted_id"],
        schema: json["schema"],
        entityId: json["entity_id"],
        name: json["name"],
        attributesInserted: json["attributes_inserted"],
        originalEntityId: json["original_entity_id"],
        attributeId: json["attribute_id"],
        originalAttributeId: json["original_attribute_id"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "inserted_id": insertedId,
        "schema": schema,
        "entity_id": entityId,
        "name": name,
        "attributes_inserted": attributesInserted,
        "original_entity_id": originalEntityId,
        "attribute_id": attributeId,
        "original_attribute_id": originalAttributeId,
      };
}
