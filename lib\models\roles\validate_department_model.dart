// To parse this JSON data, do
//
//     final validateDepartmentModel = validateDepartmentModelFromJson(jsonString);

import 'dart:convert';

ValidateDepartmentModel validateDepartmentModelFromJson(String str) => ValidateDepartmentModel.fromJson(json.decode(str));

String validateDepartmentModelToJson(ValidateDepartmentModel data) => json.encode(data.toJson());

class ValidateDepartmentModel {
    ParsedData? parsedData;
    ValidationResult? validationResult;
    List<String>? dependencyErrors;
    UniquenessResult? uniquenessResult;
    bool? isValid;
    Issues? issues;
    bool? hasErrors;
    bool? hasWarnings;
    IssueCounts? issueCounts;
    bool? success;
    String? failureReason;

    ValidateDepartmentModel({
        this.parsedData,
        this.validationResult,
        this.dependencyErrors,
        this.uniquenessResult,
        this.isValid,
        this.issues,
        this.hasErrors,
        this.hasWarnings,
        this.issueCounts,
        this.success,
        this.failureReason,
    });

    ValidateDepartmentModel copyWith({
        ParsedData? parsedData,
        ValidationResult? validationResult,
        List<String>? dependencyErrors,
        UniquenessResult? uniquenessResult,
        bool? isValid,
        Issues? issues,
        bool? hasErrors,
        bool? hasWarnings,
        IssueCounts? issueCounts,
        bool? success,
        String? failureReason,
    }) => 
        ValidateDepartmentModel(
            parsedData: parsedData ?? this.parsedData,
            validationResult: validationResult ?? this.validationResult,
            dependencyErrors: dependencyErrors ?? this.dependencyErrors,
            uniquenessResult: uniquenessResult ?? this.uniquenessResult,
            isValid: isValid ?? this.isValid,
            issues: issues ?? this.issues,
            hasErrors: hasErrors ?? this.hasErrors,
            hasWarnings: hasWarnings ?? this.hasWarnings,
            issueCounts: issueCounts ?? this.issueCounts,
            success: success ?? this.success,
            failureReason: failureReason ?? this.failureReason,
        );

    factory ValidateDepartmentModel.fromJson(Map<String, dynamic> json) => ValidateDepartmentModel(
        parsedData: json["parsed_data"] == null ? null : ParsedData.fromJson(json["parsed_data"]),
        validationResult: json["validation_result"] == null ? null : ValidationResult.fromJson(json["validation_result"]),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<String>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessResult: json["uniqueness_result"] == null ? null : UniquenessResult.fromJson(json["uniqueness_result"]),
        isValid: json["is_valid"],
        issues: json["issues"] == null ? null : Issues.fromJson(json["issues"]),
        hasErrors: json["has_errors"],
        hasWarnings: json["has_warnings"],
        issueCounts: json["issue_counts"] == null ? null : IssueCounts.fromJson(json["issue_counts"]),
        success: json["success"],
        failureReason: json["failure_reason"],
    );

    Map<String, dynamic> toJson() => {
        "parsed_data": parsedData?.toJson(),
        "validation_result": validationResult?.toJson(),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_result": uniquenessResult?.toJson(),
        "is_valid": isValid,
        "issues": issues?.toJson(),
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "issue_counts": issueCounts?.toJson(),
        "success": success,
        "failure_reason": failureReason,
    };
}

class IssueCounts {
    int? totalErrors;
    int? totalWarnings;
    int? totalExceptions;
    int? validationErrors;
    int? dependencyErrors;
    int? uniquenessIssues;
    int? parsingIssues;
    int? mongoErrors;
    int? postgresErrors;
    bool? hasCriticalErrors;
    bool? hasWarnings;

    IssueCounts({
        this.totalErrors,
        this.totalWarnings,
        this.totalExceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
        this.hasCriticalErrors,
        this.hasWarnings,
    });

    IssueCounts copyWith({
        int? totalErrors,
        int? totalWarnings,
        int? totalExceptions,
        int? validationErrors,
        int? dependencyErrors,
        int? uniquenessIssues,
        int? parsingIssues,
        int? mongoErrors,
        int? postgresErrors,
        bool? hasCriticalErrors,
        bool? hasWarnings,
    }) => 
        IssueCounts(
            totalErrors: totalErrors ?? this.totalErrors,
            totalWarnings: totalWarnings ?? this.totalWarnings,
            totalExceptions: totalExceptions ?? this.totalExceptions,
            validationErrors: validationErrors ?? this.validationErrors,
            dependencyErrors: dependencyErrors ?? this.dependencyErrors,
            uniquenessIssues: uniquenessIssues ?? this.uniquenessIssues,
            parsingIssues: parsingIssues ?? this.parsingIssues,
            mongoErrors: mongoErrors ?? this.mongoErrors,
            postgresErrors: postgresErrors ?? this.postgresErrors,
            hasCriticalErrors: hasCriticalErrors ?? this.hasCriticalErrors,
            hasWarnings: hasWarnings ?? this.hasWarnings,
        );

    factory IssueCounts.fromJson(Map<String, dynamic> json) => IssueCounts(
        totalErrors: json["total_errors"],
        totalWarnings: json["total_warnings"],
        totalExceptions: json["total_exceptions"],
        validationErrors: json["validation_errors"],
        dependencyErrors: json["dependency_errors"],
        uniquenessIssues: json["uniqueness_issues"],
        parsingIssues: json["parsing_issues"],
        mongoErrors: json["mongo_errors"],
        postgresErrors: json["postgres_errors"],
        hasCriticalErrors: json["has_critical_errors"],
        hasWarnings: json["has_warnings"],
    );

    Map<String, dynamic> toJson() => {
        "total_errors": totalErrors,
        "total_warnings": totalWarnings,
        "total_exceptions": totalExceptions,
        "validation_errors": validationErrors,
        "dependency_errors": dependencyErrors,
        "uniqueness_issues": uniquenessIssues,
        "parsing_issues": parsingIssues,
        "mongo_errors": mongoErrors,
        "postgres_errors": postgresErrors,
        "has_critical_errors": hasCriticalErrors,
        "has_warnings": hasWarnings,
    };
}

class Issues {
    IssueCounts? summary;
    List<dynamic>? errors;
    List<dynamic>? warnings;
    List<dynamic>? exceptions;
    List<dynamic>? validationErrors;
    List<DependencyError>? dependencyErrors;
    List<dynamic>? uniquenessIssues;
    List<dynamic>? parsingIssues;
    List<dynamic>? mongoErrors;
    List<dynamic>? postgresErrors;

    Issues({
        this.summary,
        this.errors,
        this.warnings,
        this.exceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
    });

    Issues copyWith({
        IssueCounts? summary,
        List<dynamic>? errors,
        List<dynamic>? warnings,
        List<dynamic>? exceptions,
        List<dynamic>? validationErrors,
        List<DependencyError>? dependencyErrors,
        List<dynamic>? uniquenessIssues,
        List<dynamic>? parsingIssues,
        List<dynamic>? mongoErrors,
        List<dynamic>? postgresErrors,
    }) => 
        Issues(
            summary: summary ?? this.summary,
            errors: errors ?? this.errors,
            warnings: warnings ?? this.warnings,
            exceptions: exceptions ?? this.exceptions,
            validationErrors: validationErrors ?? this.validationErrors,
            dependencyErrors: dependencyErrors ?? this.dependencyErrors,
            uniquenessIssues: uniquenessIssues ?? this.uniquenessIssues,
            parsingIssues: parsingIssues ?? this.parsingIssues,
            mongoErrors: mongoErrors ?? this.mongoErrors,
            postgresErrors: postgresErrors ?? this.postgresErrors,
        );

    factory Issues.fromJson(Map<String, dynamic> json) => Issues(
        summary: json["summary"] == null ? null : IssueCounts.fromJson(json["summary"]),
        errors: json["errors"] == null ? [] : List<dynamic>.from(json["errors"]!.map((x) => x)),
        warnings: json["warnings"] == null ? [] : List<dynamic>.from(json["warnings"]!.map((x) => x)),
        exceptions: json["exceptions"] == null ? [] : List<dynamic>.from(json["exceptions"]!.map((x) => x)),
        validationErrors: json["validation_errors"] == null ? [] : List<dynamic>.from(json["validation_errors"]!.map((x) => x)),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<DependencyError>.from(json["dependency_errors"]!.map((x) => DependencyError.fromJson(x))),
        uniquenessIssues: json["uniqueness_issues"] == null ? [] : List<dynamic>.from(json["uniqueness_issues"]!.map((x) => x)),
        parsingIssues: json["parsing_issues"] == null ? [] : List<dynamic>.from(json["parsing_issues"]!.map((x) => x)),
        mongoErrors: json["mongo_errors"] == null ? [] : List<dynamic>.from(json["mongo_errors"]!.map((x) => x)),
        postgresErrors: json["postgres_errors"] == null ? [] : List<dynamic>.from(json["postgres_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "summary": summary?.toJson(),
        "errors": errors == null ? [] : List<dynamic>.from(errors!.map((x) => x)),
        "warnings": warnings == null ? [] : List<dynamic>.from(warnings!.map((x) => x)),
        "exceptions": exceptions == null ? [] : List<dynamic>.from(exceptions!.map((x) => x)),
        "validation_errors": validationErrors == null ? [] : List<dynamic>.from(validationErrors!.map((x) => x)),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x.toJson())),
        "uniqueness_issues": uniquenessIssues == null ? [] : List<dynamic>.from(uniquenessIssues!.map((x) => x)),
        "parsing_issues": parsingIssues == null ? [] : List<dynamic>.from(parsingIssues!.map((x) => x)),
        "mongo_errors": mongoErrors == null ? [] : List<dynamic>.from(mongoErrors!.map((x) => x)),
        "postgres_errors": postgresErrors == null ? [] : List<dynamic>.from(postgresErrors!.map((x) => x)),
    };
}

class DependencyError {
    String? message;
    String? source;
    DateTime? timestamp;

    DependencyError({
        this.message,
        this.source,
        this.timestamp,
    });

    DependencyError copyWith({
        String? message,
        String? source,
        DateTime? timestamp,
    }) => 
        DependencyError(
            message: message ?? this.message,
            source: source ?? this.source,
            timestamp: timestamp ?? this.timestamp,
        );

    factory DependencyError.fromJson(Map<String, dynamic> json) => DependencyError(
        message: json["message"],
        source: json["source"],
        timestamp: json["timestamp"] == null ? null : DateTime.parse(json["timestamp"]),
    );

    Map<String, dynamic> toJson() => {
        "message": message,
        "source": source,
        "timestamp": timestamp?.toIso8601String(),
    };
}

class ParsedData {
    int? id;
    String? tenantId;
    String? name;
    String? description;
    String? departmentHeadRoleId;
    int? parentDepartmentId;
    String? naturalLanguage;
    DateTime? createdAt;
    dynamic createdBy;
    DateTime? updatedAt;
    dynamic updatedBy;
    int? version;
    String? status;

    ParsedData({
        this.id,
        this.tenantId,
        this.name,
        this.description,
        this.departmentHeadRoleId,
        this.parentDepartmentId,
        this.naturalLanguage,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
        this.version,
        this.status,
    });

    ParsedData copyWith({
        int? id,
        String? tenantId,
        String? name,
        String? description,
        String? departmentHeadRoleId,
        int? parentDepartmentId,
        String? naturalLanguage,
        DateTime? createdAt,
        dynamic createdBy,
        DateTime? updatedAt,
        dynamic updatedBy,
        int? version,
        String? status,
    }) => 
        ParsedData(
            id: id ?? this.id,
            tenantId: tenantId ?? this.tenantId,
            name: name ?? this.name,
            description: description ?? this.description,
            departmentHeadRoleId: departmentHeadRoleId ?? this.departmentHeadRoleId,
            parentDepartmentId: parentDepartmentId ?? this.parentDepartmentId,
            naturalLanguage: naturalLanguage ?? this.naturalLanguage,
            createdAt: createdAt ?? this.createdAt,
            createdBy: createdBy ?? this.createdBy,
            updatedAt: updatedAt ?? this.updatedAt,
            updatedBy: updatedBy ?? this.updatedBy,
            version: version ?? this.version,
            status: status ?? this.status,
        );

    factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        id: json["id"],
        tenantId: json["tenant_id"],
        name: json["name"],
        description: json["description"],
        departmentHeadRoleId: json["department_head_role_id"],
        parentDepartmentId: json["parent_department_id"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
        version: json["version"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "tenant_id": tenantId,
        "name": name,
        "description": description,
        "department_head_role_id": departmentHeadRoleId,
        "parent_department_id": parentDepartmentId,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
        "version": version,
        "status": status,
    };
}

class UniquenessResult {
    bool? isUnique;
    String? status;
    String? message;

    UniquenessResult({
        this.isUnique,
        this.status,
        this.message,
    });

    UniquenessResult copyWith({
        bool? isUnique,
        String? status,
        String? message,
    }) => 
        UniquenessResult(
            isUnique: isUnique ?? this.isUnique,
            status: status ?? this.status,
            message: message ?? this.message,
        );

    factory UniquenessResult.fromJson(Map<String, dynamic> json) => UniquenessResult(
        isUnique: json["is_unique"],
        status: json["status"],
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "is_unique": isUnique,
        "status": status,
        "message": message,
    };
}

class ValidationResult {
    List<dynamic>? structureErrors;
    List<dynamic>? requiredFieldErrors;
    List<dynamic>? dataTypeErrors;
    List<dynamic>? customErrors;

    ValidationResult({
        this.structureErrors,
        this.requiredFieldErrors,
        this.dataTypeErrors,
        this.customErrors,
    });

    ValidationResult copyWith({
        List<dynamic>? structureErrors,
        List<dynamic>? requiredFieldErrors,
        List<dynamic>? dataTypeErrors,
        List<dynamic>? customErrors,
    }) => 
        ValidationResult(
            structureErrors: structureErrors ?? this.structureErrors,
            requiredFieldErrors: requiredFieldErrors ?? this.requiredFieldErrors,
            dataTypeErrors: dataTypeErrors ?? this.dataTypeErrors,
            customErrors: customErrors ?? this.customErrors,
        );

    factory ValidationResult.fromJson(Map<String, dynamic> json) => ValidationResult(
        structureErrors: json["structure_errors"] == null ? [] : List<dynamic>.from(json["structure_errors"]!.map((x) => x)),
        requiredFieldErrors: json["required_field_errors"] == null ? [] : List<dynamic>.from(json["required_field_errors"]!.map((x) => x)),
        dataTypeErrors: json["data_type_errors"] == null ? [] : List<dynamic>.from(json["data_type_errors"]!.map((x) => x)),
        customErrors: json["custom_errors"] == null ? [] : List<dynamic>.from(json["custom_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "structure_errors": structureErrors == null ? [] : List<dynamic>.from(structureErrors!.map((x) => x)),
        "required_field_errors": requiredFieldErrors == null ? [] : List<dynamic>.from(requiredFieldErrors!.map((x) => x)),
        "data_type_errors": dataTypeErrors == null ? [] : List<dynamic>.from(dataTypeErrors!.map((x) => x)),
        "custom_errors": customErrors == null ? [] : List<dynamic>.from(customErrors!.map((x) => x)),
    };
}
