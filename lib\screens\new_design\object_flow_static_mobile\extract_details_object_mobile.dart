import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/create_object_mobile.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/industry_bundles_object_mobile.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/all_smart_resolution_object_mobile.dart';
import 'package:nsl/screens/web/static_flow/components/manual_processing_component.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';

class ExtractDetailsObjectMobile extends StatefulWidget {
  const ExtractDetailsObjectMobile({super.key});

  @override
  State<ExtractDetailsObjectMobile> createState() =>
      _ExtractDetailsObjectMobileState();
}

class _ExtractDetailsObjectMobileState
    extends State<ExtractDetailsObjectMobile> {
  late AccordionController _accordionController;
  bool _showCloseIcon = false; // Show sparkle icon initially
  bool _showCloseIconIndustry = false; // Show industry icon initially

  bool showOnboardingBox = false;

  // Track expanded state for accordion items
  Map<String, bool> expandedStates = {
    'Object Details': false,
    'Attributes Details': false,
    'Entity Relationship': false,
    'Attribute Business Rules': false,
    'Enumerated Values': false,
    'System Permissions': false,
    'Security Classification': false,
  };

  // Track expanded state for object items in AI mode
  Map<String, bool> objectExpandedStates = {
    'Object: Customer': false,
    'Object: Product': false,
    'Object: Order': false,
  };

  // Data storage for tables
  List<Map<String, String>> _attributeData = [
    {
      'attributeName': 'customer_id',
      'displayName': 'Customer ID',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'YES'
    },
    {
      'attributeName': 'email',
      'displayName': 'Email Address',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'YES'
    },
    {
      'attributeName': 'name',
      'displayName': 'Full Name',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'NO'
    },
  ];

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            surfaceTintColor: Colors.transparent,
            leading: Builder(
              builder: (context) => IconButton(
                icon: Icon(Icons.menu),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
            ),
            title: Text(''),
            backgroundColor: Color(0xffF7F9FB),
            elevation: 0,
            iconTheme: IconThemeData(color: Colors.black),
          ),
          drawer: const CustomDrawer(),
          backgroundColor: Colors.black,
          body: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area with positioned bottom actions
              Expanded(
                child: Stack(
                  children: [
                    Container(
                      color: Colors.white,
                      child: _buildContent(context, provider),
                    ),
                    // Bottom action buttons positioned on top
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: _buildBottomActions(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left side with title and subtitle
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Dynamic header label based on toggle state
              Text(
                provider.isAIMode ? 'Extracted Details' : 'Objects',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 4),
              // Manually Process subtitle
              Text(
                'Manually Process',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  color: Colors.white.withValues(alpha: 0.8),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  height: 1.2,
                ),
              ),
            ],
          ),

          // Right side with Form label and toggle
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Form label
              Text(
                'Form',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  height: 1,
                ),
              ),
              const SizedBox(width: 12),
              // Toggle switch
              _buildFormToggle(context, provider),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Close entity details panel if it's open before switching modes
          final manualProvider =
              Provider.of<ManualCreationProvider>(context, listen: false);
          if (manualProvider.selectedEntity != null) {
            manualProvider.setSelectedEntity(null);
          }

          provider.toggleAIMode();
          manualProvider.handleEntityValidationForBook();
        },
        child: Container(
          width: 44,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: AnimatedAlign(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            alignment: provider.isAIMode
                ? Alignment.centerLeft
                : Alignment.centerRight,
            child: Container(
              width: 20,
              height: 20,
              margin: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Color(0xFF0058FF),
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (!provider.isAIMode) {
      return _buildObjectsTab(context);
    } else {
      return _buildExtractedDetailsTab(context);
    }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return SizedBox(
      height: double.infinity,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Expansion panels for objects in extracted details
            _buildObjectItem(context, 'Object: Customer'),
            const SizedBox(height: 12),
            _buildObjectItem(context, 'Object: Product'),
            const SizedBox(height: 12),
            _buildObjectItem(context, 'Object: Order'),
            const SizedBox(height: 12),
            _buildObjectItem(context, 'Solution: Customer Onboarding'),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return const ManualProcessingComponent();
  }

  Widget _buildObjectItem(BuildContext context, String title) {
    bool isExpanded = objectExpandedStates[title] ?? false;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        // Close all other object accordions when opening one
                        objectExpandedStates.updateAll((key, value) => false);
                        objectExpandedStates[title] = !isExpanded;
                      });
                    },
                    child: Text(
                      title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                        fontWeight:
                            isExpanded ? FontWeight.bold : FontWeight.w500,
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => _showNotificationPopup(context),
                    child: SizedBox(
                      width: 30,
                      height: 30,
                      child: Icon(
                        Icons.notifications_outlined,
                        size: 24,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (isExpanded)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              margin: EdgeInsets.only(bottom: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Conditionally show static text only for Customer Onboarding
                  if (title == 'Solution: Customer Onboarding') ...[
                    // Container(
                    //   height: 0.66, // Approx. 0.5pt
                    //   width: 457, // Approx. 343pt
                    //   color: Color(0xFF0058FF),
                    //   margin: const EdgeInsets.only(
                    //       bottom: 8), // spacing before text
                    // ),
                    const SizedBox(height: 11),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          showOnboardingBox = !showOnboardingBox;
                        });
                      },
                      child: Text(
                        'Customer Onboarding',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Color(0xFF242424),
                        ),
                      ),
                    ),
                    const SizedBox(height: 22),

                    // Conditional display
                    if (!showOnboardingBox) ...[
                      Text(
                        '1. System verifies Budget availability.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '2. Requester creates Purchase Requisition.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '3. System validates Purchase Requisition.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '4. Director reviews High-Value Purchase Requisition.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '5. Procurement Officer creates Purchase Order.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '6. System Initiates Parallel Assessment Tasks.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '7. Manager reviews Purchase Requisition.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                      const SizedBox(height: 22),
                      Text(
                        '8. System notifies Requester of Rejection.',
                        style: TextStyle(
                          fontFamily: 'Tiempos Text',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xFF242424),
                        ),
                      ),
                    ] else ...[
                      SingleChildScrollView(
                        padding: const EdgeInsets.symmetric(vertical: 0),
                        child: Column(
                          children: [
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Core Metadata',
                              'Completed',
                              'Process Identified',
                              const Color(0xFFD1FAE5),
                              const Color(0xFF065F46),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Process Ownership',
                              'Completed',
                              '3 roles',
                              const Color(0xFFD1FAE5),
                              const Color(0xFF065F46),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Trigger Definition',
                              'Completed',
                              'User-initiated',
                              const Color(0xFFD1FAE5),
                              const Color(0xFF065F46),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Local Objectives',
                              'Missing',
                              'User-initiated',
                              const Color(0xFFFEE2E2),
                              const Color(0xFF991B1B),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Pathway Definitions',
                              'Missing',
                              'Manual Configuration',
                              const Color(0xFFFEE2E2),
                              const Color(0xFF991B1B),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Pathway Detail',
                              'Not Configured',
                              'Manual Configuration',
                              const Color(0xFFFEE2E2),
                              const Color(0xFF991B1B),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Business Rules',
                              'Not Configured',
                              'Manual Configuration',
                              const Color(0xFFFEE2E2),
                              const Color(0xFF991B1B),
                              false,
                            ),
                            _buildNestedAccordionItemObjectFlow(
                              context,
                              'Validation Rules',
                              'Not Configured',
                              'Manual Configuration',
                              const Color(0xFFFEE2E2),
                              const Color(0xFF991B1B),
                              false,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ] else ...[
                    _buildNestedAccordionItem(
                      context,
                      'Object Details',
                      'Partial Completion',
                      '1 Entity Detected',
                      const Color(0xFFFEF3C7),
                      const Color(0xFF92400E),
                      true,
                    ),
                    _buildNestedAccordionItem(
                      context,
                      'Attributes Details',
                      'Completed',
                      '25 Attributes',
                      const Color(0xFFD1FAE5),
                      const Color(0xFF065F46),
                      true,
                    ),
                    _buildNestedAccordionItem(
                      context,
                      'Entity Relationships',
                      'Partial Completion',
                      '0 rules Configured',
                      const Color(0xFFFEF3C7),
                      const Color(0xFF92400E),
                      false,
                    ),
                    _buildNestedAccordionItem(
                      context,
                      'Attribute Business Rules',
                      'Missing',
                      '0 Configure',
                      const Color(0xFFFEE2E2),
                      const Color(0xFF991B1B),
                      false,
                    ),
                    _buildNestedAccordionItem(
                      context,
                      'Enumerated Values',
                      'Missing',
                      '0 Configure',
                      const Color(0xFFFEE2E2),
                      const Color(0xFF991B1B),
                      false,
                    ),
                    _buildNestedAccordionItem(
                      context,
                      'System Permissions',
                      'Not Configured',
                      '0 Configure',
                      const Color(0xFFFEE2E2),
                      const Color(0xFF991B1B),
                      false,
                    ),
                    _buildNestedAccordionItem(
                      context,
                      'Security Classification',
                      'Not Configured',
                      '0 Configure',
                      const Color(0xFFFEE2E2),
                      const Color(0xFF991B1B),
                      false,
                    ),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNestedAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('nested_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            InkWell(
              onTap: () {
                _accordionController.togglePanel('nested_$title');
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: Row(
                  children: [
                    // Title Text
                    Expanded(
                      child: Text(
                        title,
                        style: FontManager.getCustomStyle(
                          fontSize: 16,
                          fontWeight:
                              isExpanded ? FontWeight.bold : FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    // Status Badge and Count in a column on the right
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          count,
                          style: FontManager.getCustomStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: backgroundColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            status,
                            style: FontManager.getCustomStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (isExpanded) ...[
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: title == 'Object Details'
                    ? _buildObjectDetailsFormView(context)
                    : title == 'Attributes Details'
                        ? _buildMobileAttributeTable(context)
                        : _buildPlaceholderContent(context, title),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNestedAccordionItemObjectFlow(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('nested_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            InkWell(
              onTap: () {
                _accordionController.togglePanel('nested_$title');
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: Row(
                  children: [
                    // Title Text
                    Expanded(
                      child: Text(
                        title,
                        style: FontManager.getCustomStyle(
                          fontSize: 16,
                          fontWeight:
                              isExpanded ? FontWeight.bold : FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    // Status Badge and Count in a column on the right
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          count,
                          style: FontManager.getCustomStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: backgroundColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            status,
                            style: FontManager.getCustomStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (isExpanded) ...[
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: _buildPlaceholderContent(context, title),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildObjectDetailsFormView(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Table Header
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                child: Row(
                  children: [
                    SizedBox(
                      width: 120,
                      child: Text(
                        'Field',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 200,
                      child: Text(
                        'Value',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 120,
                      child: Text(
                        'Source',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Table Rows
              _buildTableRow(context, 'Entity Name', 'Customer', 'Extracted'),
              _buildTableRow(
                  context, 'Display Name', 'Customer', 'Auto-generated'),
              _buildTableRow(context, 'Type', 'Master', 'Inferred'),
              _buildTableRow(
                  context, 'Business Domain', 'E-commerce', 'Extracted'),
              _buildTableRow(context, 'Description', 'Core customer data with',
                  'Generated'),
              _buildTableRow(context, 'Category', '', 'Inferred'),
              _buildTableRow(context, 'Tags', '', 'Extracted'),
              _buildTableRow(
                  context, 'Archival Strategy', 'Archive only', 'Inferred'),
              _buildTableRow(context, 'Colour Theme', 'Blue', 'Inferred'),
              _buildTableRow(context, 'Icon', '', 'Auto-generated'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTableRow(
      BuildContext context, String field, String value, String source) {
    // Determine if this field should be a dropdown
    bool isDropdown = _isDropdownField(field);

    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Row(
        children: [
          // Field label
          SizedBox(
            width: 120,
            child: Text(
              field,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          // Input field or dropdown
          SizedBox(
            width: 200,
            child: isDropdown
                ? _buildDropdownInput(context, field, value)
                : _buildTextInput(context, field, value),
          ),
          // Source chip
          const SizedBox(width: 12),
          SizedBox(
            width: 120,
            child: _buildSourceChip(context, source),
          ),
        ],
      ),
    );
  }

  Widget _buildSourceChip(BuildContext context, String source) {
    Color chipColor;
    IconData chipIcon;

    switch (source) {
      case 'Extracted':
        chipColor = Color.fromARGB(255, 81, 138, 73);
        chipIcon = Icons.add;
        break;
      case 'Auto-generated':
        chipColor = Color.fromARGB(255, 119, 177, 212);
        chipIcon = Icons.percent;
        break;
      case 'Inferred':
        chipColor = Color(0xFFF8DCC1);
        chipIcon = Icons.location_on;
        break;
      case 'Generated':
        chipColor = Colors.purple;
        chipIcon = Icons.percent;
        break;
      default:
        chipColor = Colors.grey;
        chipIcon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            chipIcon,
            size: 12,
            color: chipColor,
          ),
          const SizedBox(width: 4),
          Text(
            source,
            style: FontManager.getCustomStyle(
              fontSize: 9,
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormField(BuildContext context, String field, String value,
      String actionText, Color actionColor) {
    // Determine if this field should be a dropdown
    bool isDropdown = _isDropdownField(field);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          // Field label
          Expanded(
            flex: 2,
            child: Text(
              field,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          // Input field or dropdown
          Expanded(
            flex: 3,
            child: isDropdown
                ? _buildDropdownInput(context, field, value)
                : _buildTextInput(context, field, value),
          ),
          const SizedBox(width: 8),
          // Action button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getActionButtonColor(actionText),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              actionText,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isDropdownField(String field) {
    return field == 'Type' ||
        field == 'Archival Strategy' ||
        field == 'Colour Theme';
  }

  Widget _buildTextInput(BuildContext context, String field, String value) {
    return Container(
      height: 36,
      child: TextFormField(
        initialValue: value,
        style: FontManager.getCustomStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Color(0xffCECECE), width: 0.5),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Color(0xffCECECE), width: 0.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF0058FF), width: 0.5),
          ),
          filled: true,
          fillColor: Colors.white,
          hintText: value.isEmpty ? 'Enter ${field.toLowerCase()}' : null,
          hintStyle: FontManager.getCustomStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[400],
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownInput(BuildContext context, String field, String value) {
    List<String> options = _getDropdownOptions(field);
    String selectedValue =
        value.isNotEmpty && options.contains(value) ? value : options.first;

    return Container(
      height: 36,
      child: DropdownButtonFormField<String>(
        value: selectedValue,
        style: FontManager.getCustomStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Color(0xffCECECE), width: 0.5),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Color(0xffCECECE), width: 0.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF0058FF), width: 0.5),
          ),
          filled: true,
          fillColor: Colors.white,
        ),
        items: options.map((String option) {
          return DropdownMenuItem<String>(
            value: option,
            child: Text(
              option,
              style: FontManager.getCustomStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          // Handle dropdown change
        },
        icon: Icon(
          Icons.keyboard_arrow_down,
          color: Colors.grey[600],
          size: 20,
        ),
      ),
    );
  }

  List<String> _getDropdownOptions(String field) {
    switch (field) {
      case 'Type':
        return ['Master', 'Transaction', 'Reference', 'Lookup'];
      case 'Archival Strategy':
        return [
          'Archive only',
          'Delete after 1 year',
          'Delete after 5 years',
          'Never delete'
        ];
      case 'Colour Theme':
        return ['Blue', 'Red', 'Green', 'Purple', 'Orange', 'Yellow'];
      default:
        return ['Option 1', 'Option 2'];
    }
  }

  Color _getActionButtonColor(String actionText) {
    switch (actionText.toLowerCase()) {
      case 'edit':
        return Colors.green;
      case 'add':
        return Colors.blue;
      case 'info':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Widget _buildMobileAttributeTable(BuildContext context) {
    return Container(
      // padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Attribute Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddAttributeModal(context),
                icon: const Icon(Icons.add, size: 14),
                label: const Text('Add Attribute'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  elevation: 0,
                  textStyle: FontManager.getCustomStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with internal scroll and fixed last column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable table section (header + body)
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Scrollable header
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF2F5F8),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child: Row(
                            children: [
                              SizedBox(
                                width: 150,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'ATTRIBUTE NAME',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 150,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'DISPLAY NAME',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 120,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'DATA TYPE',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 110,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'REQUIRED',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 110,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'UNIQUE',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Scrollable body
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            child: Column(
                              children:
                                  _attributeData.asMap().entries.map((entry) {
                                int index = entry.key;
                                Map<String, String> data = entry.value;
                                return _buildMobileAttributeCard(
                                    context, index, data);
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column (header + body)
                Container(
                  width: 100,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Fixed Actions header
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFF2F5F8),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            'ACTIONS',
                            style: FontManager.getCustomStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                      // Fixed Actions body
                      Container(
                        constraints: const BoxConstraints(maxHeight: 200),
                        child: SingleChildScrollView(
                          child: Column(
                            children:
                                _attributeData.asMap().entries.map((entry) {
                              int index = entry.key;
                              Map<String, String> data = entry.value;
                              return _buildAttributeActionColumn(
                                  context, index, data);
                            }).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileAttributeCard(
      BuildContext context, int index, Map<String, String> data) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Attribute Name column
          SizedBox(
            width: 150,
            child: Text(
              data['attributeName']!,
              style: FontManager.getCustomStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          // Display Name column
          SizedBox(
            width: 150,
            child: Text(
              data['displayName']!,
              style: FontManager.getCustomStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          // Data Type column
          SizedBox(
            width: 120,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              // decoration: BoxDecoration(
              //   color: Colors.blue[50],
              //   borderRadius: BorderRadius.circular(4),
              // ),
              child: Text(
                data['dataType']!,
                style: FontManager.getCustomStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          // Required column
          SizedBox(
            width: 110,
            child: data['YES'] == 'YES'
                ? Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    // decoration: BoxDecoration(
                    //   color: Colors.orange[50],
                    //   borderRadius: BorderRadius.circular(4),
                    // ),
                    child: Text(
                      'YES',
                      style: FontManager.getCustomStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  )
                : Text(
                    'NO',
                    style: FontManager.getCustomStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
          ),
          // Unique column
          SizedBox(
            width: 110,
            child: data['YES'] == 'YES'
                ? Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    // decoration: BoxDecoration(
                    //   color: Colors.green[50],
                    //   borderRadius: BorderRadius.circular(4),
                    // ),
                    child: Text(
                      'YES',
                      style: FontManager.getCustomStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  )
                : Text(
                    'NO',
                    style: FontManager.getCustomStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeActionColumn(
      BuildContext context, int index, Map<String, String> data) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 22,
            width: 21,
            child: IconButton(
              iconSize: 20,
              onPressed: () => _showEditAttributeModal(
                context,
                index,
                data['attributeName']!,
                data['displayName']!,
                data['dataType']!,
                data['required']!,
                data['unique']!,
              ),
              icon: const Icon(Icons.edit_outlined),
              color: Colors.blue[600],
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              tooltip: 'Edit',
            ),
          ),
          const SizedBox(width: 12),
          SizedBox(
            height: 22,
            width: 21,
            child: IconButton(
              onPressed: () =>
                  _deleteAttribute(context, index, data['attributeName']!),
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 20,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              tooltip: 'Delete',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPlaceholderTitle(title),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddModal(context, title),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  _getAddButtonText(title),
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  alignment: Alignment.center, // Ensures vertical center
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with internal scroll and fixed last column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable table section (header + body)
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Scrollable header
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child:
                              _buildScrollableFormTableHeader(context, title),
                        ),
                        // Scrollable body
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            child: Column(
                              children:
                                  _buildScrollableFormTableRows(context, title),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column (header + body)
                Container(
                  // width: 120,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Fixed Actions header
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9FAFB),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        child: Text(
                          'ACTIONS',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      // Fixed Actions body
                      Container(
                        // constraints: const BoxConstraints(maxHeight: 200),
                        child: SingleChildScrollView(
                          child: Column(
                            children: _buildFixedActionsColumn(context, title),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableFormTableHeader(BuildContext context, String title) {
    List<String> headers =
        _getTableHeaders(title); // Get headers without ACTIONS
    List<double> widths =
        _getColumnWidths(title); // Get widths without actions column

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        double width = widths[index];

        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  List<Widget> _buildFixedActionsColumn(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 20.5,
              width: 20,
              child: IconButton(
                iconSize: 16,
                onPressed: () => _showEditModal(context, index, title, rowData),
                icon: const Icon(Icons.edit_outlined),
                color: Colors.blue[600],
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                tooltip: 'Edit',
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              height: 20.5,
              width: 20,
              child: IconButton(
                onPressed: () => _showDeleteConfirmationGeneric(
                    context, index, title, rowData[0]),
                icon: const Icon(Icons.delete_outline),
                color: Colors.red[600],
                iconSize: 16,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                tooltip: 'Delete',
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  List<List<String>> _getTableData(String title) {
    switch (title) {
      case 'Object Details':
        return [
          [
            'object_name',
            'Customer',
            'string',
            'YES',
            'Primary object identifier'
          ],
          [
            'created_date',
            '2024-01-15',
            'date',
            'YES',
            'Object creation timestamp'
          ],
          ['version', '1.0', 'string', 'NO', 'Object version number'],
        ];
      case 'Entity Relationships':
        return [
          ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
          ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
          ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
        ];
      case 'Attribute Business Rules':
        return [
          ['email_validation', 'email', 'format = email', 'reject', 'High'],
          ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
          ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
        ];
      case 'Enumerated Values':
        return [
          [
            'status_enum',
            'Active, Inactive, Pending',
            'Active',
            'Customer status options',
            'Active'
          ],
          [
            'type_enum',
            'Individual, Corporate',
            'Individual',
            'Customer type classification',
            'Active'
          ],
          [
            'priority_enum',
            'Low, Medium, High',
            'Medium',
            'Priority levels',
            'Active'
          ],
        ];
      case 'System Permissions':
        return [
          ['read_customer', 'User', 'Read', 'Customer Data', 'Active'],
          ['write_customer', 'Admin', 'Write', 'Customer Data', 'Active'],
          [
            'delete_customer',
            'SuperAdmin',
            'Delete',
            'Customer Data',
            'Restricted'
          ],
        ];
      case 'Security Classification':
        return [
          [
            'PII_Data',
            'High',
            'email, phone, address',
            'Encryption Required',
            'Active'
          ],
          ['Public_Data', 'Low', 'name, company', 'No Restrictions', 'Active'],
          [
            'Financial_Data',
            'Critical',
            'payment_info',
            'Strict Access Control',
            'Active'
          ],
        ];
      case 'Core Metadata':
        return [
          ['Name', 'Customer Onboardig', 'SOURCE'],
          ['Display Name', 'Customer Onboarding Process', 'SOURCE'],
          ['Primary Entity', 'Primary_Entity', 'SOURCE'],
        ];
      case 'Process Ownership':
        return [
          ['Originator', 'Role who initiates', 'In details'],
          ['Executive', 'Role responsible for', 'In details'],
          ['Business Sponsor', 'Department Role', 'In details'],
        ];
      case 'Trigger Definition':
        return [
          [
            'Originator',
            'Role who initiates',
            'trigger condition',
            'trigger schedule'
          ],
          [
            'Originator testing',
            'Role who finished',
            'trigger condition',
            'trigger schedule'
          ]
        ];
      case 'Local Objectives':
        return [
          ['LO-1', 'Validates Registration', 'agent type', 'In details'],
          ['LO-2', 'Validates Registration', 'agent type', 'In details'],
          ['LO-3', 'Validates Registration', 'agent type', 'In details'],
        ];
      case 'Pathway Definitions':
        return [
          ['Happy Path', 'Successfull Onbaord', 'types', 'description'],
          ['Happy Path', 'Successfull Onbaord', 'types', 'description']
        ];
      case 'Pathway Detail':
        return [
          [
            'ValidateRegistration',
            'Validate user reg',
            'routing condition',
            'description'
          ],
          [
            'ValidateRegistration',
            'Validate user reg',
            'routing condition',
            'description'
          ]
        ];
      case 'Business Rules':
        return [
          [
            'Executive',
            'Customer',
            'inputs',
            'description',
            'operation',
            'validation',
            'output',
            'error message'
          ],
          [
            'admin',
            'laber',
            'inputs',
            'description',
            'operation',
            'validation',
            'output',
            'error message'
          ]
        ];
      case 'Validation Rules':
        return [
          [
            'go_completences_check',
            'Validate G',
            'inputs',
            'description',
            'operation',
            'validation',
            'output',
            'error message'
          ]
        ];
      default:
        return [
          ['sample_name', 'sample_value', 'string', 'Active'],
          ['example_item', 'example_data', 'number', 'Pending'],
        ];
    }
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Left side icons in one container - stacked vertically
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const CreateObjectScreenMobile(),

                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const begin = Offset(-1.0, 0.0); // Slide from right
                    const end = Offset.zero;
                    const curve = Curves.easeInOut;
                    final tween = Tween(begin: begin, end: end)
                        .chain(CurveTween(curve: curve));
                    return SlideTransition(
                      position: animation.drive(tween),
                      child: child,
                    );
                  },
// ...existing code...
                  transitionDuration: const Duration(milliseconds: 600),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Color(0xAAD0D0D0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    offset: const Offset(0, 3), // X: 0, Y: 3
                    blurRadius: 20, // Blur: 20
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.menu,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(height: 8),
                  Icon(
                    Icons.mic_none,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                ],
              ),
            ),
          ),

          const Spacer(),

          // Right side colored circles - stacked vertically
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  if (_showCloseIcon) {
                    Navigator.pop(context);
                  } else {
                    setState(() {
                      _showCloseIcon = true;
                    });
                    Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  AllSmartResolutionObjectMobile(),
                          transitionDuration: Duration(milliseconds: 100),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) =>
                                  FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        )).then((_) {
                      // Reset the icon when returning from the screen
                      setState(() {
                        _showCloseIcon = false;
                      });
                    });
                  }
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF064CD1), Color(0xFF0093FF)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Center(
                    child: _showCloseIcon
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          )
                        : SvgPicture.asset(
                            'assets/images/object-flow-mobile/sparkle-stars-ai.svg', // Update with your SVG asset path
                            width: 30,
                            height: 30,
                          ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  if (_showCloseIconIndustry) {
                    Navigator.pop(context);
                  } else {
                    setState(() {
                      _showCloseIconIndustry = true;
                    });
                    Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  IndustryBundlesObjectMobile(),
                          transitionDuration: Duration(milliseconds: 100),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) =>
                                  FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        )).then((_) {
                      // Reset the icon when returning from the screen
                      setState(() {
                        _showCloseIconIndustry = false;
                      });
                    });
                  }
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFF673AB7),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ], // Purple color
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Center(
                    child: _showCloseIconIndustry
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          )
                        : SvgPicture.asset(
                            'assets/images/object-flow-mobile/industry-bundles.svg', // Update with your SVG asset path
                            width: 30,
                            height: 30,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildObjectHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Object: Customer',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  height: 1.2,
                ),
              ),
            ),
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => _showNotificationPopup(context),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.notifications_none,
                    size: 14,
                    color: Colors.red,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showNotificationPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Message text
                Text(
                  'This Object is already exists in your library. You need to rename the objects to proceed.',
                  textAlign: TextAlign.center,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 24),

                // Buttons row
                Row(
                  children: [
                    // Continue button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey.shade300),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: Text(
                          'Continue',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Resolve button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Navigate to AllSmartResolutionObjectMobile screen
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  const AllSmartResolutionObjectMobile(),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0058FF),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                        ),
                        child: Text(
                          'Resolve',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showEditModal(
      BuildContext context, int index, String title, List<String> rowData) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.78,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Modal content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Modal header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Edit ${_getModalTitle(title)}',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.titleLarge(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 24,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Scrollable form content
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Form fields based on section type with pre-filled values
                                ..._buildModalFormFieldsWithValues(
                                    context, title, rowData),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action buttons - Fixed at bottom
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(color: Colors.grey[300]!),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF0058FF),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Update',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildModalFormFieldsWithValues(
      BuildContext context, String title, List<String> rowData) {
    switch (title) {
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Relationship Name', rowData[0])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Target Entity', rowData[1])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'],
                      rowData[2])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(context,
                      'Cardinality', ['1:1', '1:N', 'N:M'], rowData[3])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownFieldWithValue(
              context, 'Status', ['Active', 'Pending', 'Inactive'], rowData[4]),
        ];
      case 'Core Metadata':
        return [
          Row(
            children: [
              Expanded(child: _buildModalFormField(context, 'Field', 'Name')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Value', [
                'CustomerOnboarding',
                'CustomerPreparation',
                'CustomerInterview'
              ])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Source', 'Extracted')),
            ],
          ),
        ];
      case 'Process Ownership':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Role Type',
                      ['Originator', 'Initial', 'Decision'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Assigned Role',
                      ['Customer', 'Member', 'Owner'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(
                      context, 'Description', 'Extracted')),
            ],
          ),
        ];
      case 'Trigger Definition':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Trigger Type',
                      ['user-initiated', 'Initial', 'Decision'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Trigger Attributes',
                      'Entry.attribute1.Entry.attribute2')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Trigger Condition',
                      'Condition that starts the process')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Trigger Schedule', [
                'user-initiated',
                'member-initiated',
                'owner-initiated'
              ])),
            ],
          ),
        ];

      case 'Local Objectives':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'LO ID', 'Enter LO ID')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Objective Name', 'Objective Name')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Agent Type', ['SYSTEM', 'PLATEFORM'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(
                      context, 'Description', 'Objective Description')),
            ],
          ),
        ];

      case 'Pathway Definitions':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Pathway Name', 'Pathname')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Steps', 'LO-1->LO-2->LO-N')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['Sequential', 'Vertical', 'Parallel'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(context,
                      'Description', 'Describe the role responsibility')),
            ],
          ),
        ];

      case 'Pathway Detail':
        return [
          Row(
            children: [
              Expanded(
                  child:
                      _buildModalFormField(context, 'Objective', 'Pathname')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Route Type',
                      ['Sequential', 'Vertical', 'Parallel'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Routing Conditions',
                      'If validation passes, route to SendEmailVertical.')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(
                      context,
                      'Description',
                      'Validate user registration information and email format')),
            ],
          ),
        ];

      case 'Business Rules':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Rule Name', 'e.g. email_uniquences_check')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Entity',
                      ['Entity Name', 'Order Number', 'Order Date'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Inputs',
                      'e.g. customer.email. Customer.registration')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(context,
                      'Description', 'Describe the business rule logic')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Operation', 'conditional_logic')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Validation',
                      ['PRE_INSERT', 'PRE_UPDATE', 'PRE_DELETE'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Output', 'e.g. customer with email_valid')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Error Message',
                      'Error Message when Validation fails')),
            ],
          ),
        ];

      case 'Validation Rules':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Rule Name', 'e.g. email_uniquences_check')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Entity',
                      ['Entity Name', 'Order Number', 'Order Date'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Inputs',
                      'e.g. customer.email. Customer.registration')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(context,
                      'Description', 'Describe the business rule logic')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Operation', 'validation_function')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Validation',
                      ['PRE_DEPLOY', 'PRE_UPDATE', 'PRE_DELETE'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Output',
                      'e.g. validationResult with status, message')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Error Message',
                      'Error Message when Validation fails')),
            ],
          ),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Name', rowData[0])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', rowData.length > 1 ? rowData[1] : '')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['string', 'number', 'boolean'],
                      rowData.length > 2 ? rowData[2] : 'string')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['Active', 'Inactive'],
                      rowData.length > 3 ? rowData[3] : 'Active')),
            ],
          ),
        ];
    }
  }

  List<Widget> _buildModalFormFields(BuildContext context, String title) {
    switch (title) {
      case 'Object Details':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Property Name', 'property_name')),
            ],
          ),
          const SizedBox(width: 20),
          Row(
            children: [
              Expanded(
                  child:
                      _buildModalFormField(context, 'Value', 'Property Value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['string', 'number', 'boolean', 'date'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Required', ['No', 'Yes'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormField(
            context,
            'Description',
            'Property description',
          ),
        ];
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Relationship Name', 'relationship_name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(
                      context, 'Target Entity', 'Target Entity')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Cardinality', ['1:1', '1:N', 'N:M'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownField(
              context, 'Status', ['Active', 'Pending', 'Inactive']),
        ];
      case 'Core Metadata':
        return [
          Row(
            children: [
              Expanded(child: _buildModalFormField(context, 'Field', 'Name')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Value', [
                'CustomerOnboarding',
                'CustomerPreparation',
                'CustomerInterview'
              ])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Source', 'Extracted')),
            ],
          ),
        ];
      case 'Process Ownership':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Role Type',
                      ['Originator', 'Initial', 'Decision'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Assigned Role',
                      ['Customer', 'Member', 'Owner'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(
                      context, 'Description', 'Extracted')),
            ],
          ),
        ];
      case 'Trigger Definition':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Trigger Type',
                      ['user-initiated', 'Initial', 'Decision'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Trigger Attributes',
                      'Entry.attribute1.Entry.attribute2')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Trigger Condition',
                      'Condition that starts the process')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Trigger Schedule', [
                'user-initiated',
                'member-initiated',
                'owner-initiated'
              ])),
            ],
          ),
        ];

      case 'Local Objectives':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'LO ID', 'Enter LO ID')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Objective Name', 'Objective Name')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Agent Type', ['SYSTEM', 'PLATEFORM'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(
                      context, 'Description', 'Objective Description')),
            ],
          ),
        ];

      case 'Pathway Definitions':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Pathway Name', 'Pathname')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Steps', 'LO-1->LO-2->LO-N')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['Sequential', 'Vertical', 'Parallel'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(context,
                      'Description', 'Describe the role responsibility')),
            ],
          ),
        ];

      case 'Pathway Detail':
        return [
          Row(
            children: [
              Expanded(
                  child:
                      _buildModalFormField(context, 'Objective', 'Pathname')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Route Type',
                      ['Sequential', 'Vertical', 'Parallel'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Routing Conditions',
                      'If validation passes, route to SendEmailVertical.')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(
                      context,
                      'Description',
                      'Validate user registration information and email format')),
            ],
          ),
        ];

      case 'Business Rules':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Rule Name', 'e.g. email_uniquences_check')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Entity',
                      ['Entity Name', 'Order Number', 'Order Date'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Inputs',
                      'e.g. customer.email. Customer.registration')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(context,
                      'Description', 'Describe the business rule logic')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Operation', 'conditional_logic')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Validation',
                      ['PRE_INSERT', 'PRE_UPDATE', 'PRE_DELETE'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Output', 'e.g. customer with email_valid')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Error Message',
                      'Error Message when Validation fails')),
            ],
          ),
        ];

      case 'Validation Rules':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Rule Name', 'e.g. email_uniquences_check')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Entity',
                      ['Entity Name', 'Order Number', 'Order Date'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Inputs',
                      'e.g. customer.email. Customer.registration')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalTextAreaFieldObjectFlow(context,
                      'Description', 'Describe the business rule logic')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Operation', 'validation_function')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Validation',
                      ['PRE_DEPLOY', 'PRE_UPDATE', 'PRE_DELETE'])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Output',
                      'e.g. validationResult with status, message')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Error Message',
                      'Error Message when Validation fails')),
            ],
          ),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Name', 'Enter name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(context, 'Value', 'Enter value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['string', 'number', 'boolean'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Status', ['Active', 'Inactive'])),
            ],
          ),
        ];
    }
  }

  Widget _buildModalFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value: options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownFieldWithValue(BuildContext context, String label,
      List<String> options, String selectedValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalFormFieldWithValue(
      BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: value,
          decoration: InputDecoration(
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmationGeneric(
      BuildContext context, int index, String title, String itemName) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.3,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Modal content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Modal header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Delete ${_getSingularTitle(title)}',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close),
                            iconSize: 24,
                            color: Colors.grey[600],
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Content
                      Expanded(
                        child: Text(
                          'Are you sure you want to delete "$itemName"? This action cannot be undone.',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                            height: 1.5,
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Action buttons - Fixed at bottom
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () => Navigator.of(context).pop(),
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(color: Colors.grey[300]!),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                              ),
                              child: Text(
                                'Cancel',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                // Handle delete logic here
                                Navigator.of(context).pop();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red[600],
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                elevation: 0,
                              ),
                              child: Text(
                                'Delete',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getPlaceholderTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Object Details Configuration';
      case 'Entity Relationships':
        return 'Entity Relationships Configuration';
      case 'Attribute Business Rules':
        return 'Business Rules Configuration';
      case 'Enumerated Values':
        return 'Enumerated Values Configuration';
      case 'System Permissions':
        return 'System Permissions Configuration';
      case 'Security Classification':
        return 'Security Classification Configuration';
      case 'Core Metadata':
        return 'Business Rules Configuration';
      case 'Process Ownership':
        return 'Role Assignmemt';
      case 'Trigger Definition':
        return 'Trigger Configuration';
      case 'Local Objectives':
        return 'Objective Management';
      case 'Pathway Definitions':
        return 'Pathway Management';
      case 'Pathway Detail':
        return 'Pathway Routing Configuration';
      case 'Business Rules':
        return 'Business Rules Configuration';
      case 'Validation Rules':
        return 'Validation Configuration';

      default:
        return '$title Configuration';
    }
  }

  String _getAddButtonText(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Property';
      case 'Entity Relationships':
        return 'Add Relationship';
      case 'Attribute Business Rules':
        return 'Add Rule';
      case 'Enumerated Values':
        return 'Add Enum';
      case 'System Permissions':
        return 'Add Permission';
      case 'Security Classification':
        return 'Add Classification';
      case 'Core Metadata':
        return 'Add Field';
      case 'Process Ownership':
        return 'Add Role';
      case 'Trigger Definition':
        return 'Add Tigger Definition';
      case 'Local Objectives':
        return 'Add Objective';
      case 'Pathway Definitions':
        return 'Add Pathway';
      case 'Pathway Detail':
        return 'Add Routing';
      case 'Business Rules':
        return 'Add Role';
      case 'Validation Rules':
        return 'Add Validation';
      default:
        return 'Add Item';
    }
  }

  void _showAddModal(BuildContext context, String title) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.75,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Modal content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Modal header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _getModalTitle(title),
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.titleLarge(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 24,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Scrollable form content
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Form fields based on section type
                                ..._buildModalFormFields(context, title),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action buttons - Fixed at bottom
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(color: Colors.grey[300]!),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF0058FF),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Apply This',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getSingularTitle(String title) {
    switch (title) {
      case 'Entity Relationships':
        return 'Relationship';
      case 'Object Details':
        return 'Property';
      case 'Attribute Business Rules':
        return 'Rule';
      case 'Enumerated Values':
        return 'Enum';
      case 'System Permissions':
        return 'Permission';
      case 'Security Classification':
        return 'Classification';
      case 'Core Metadata':
        return 'Business Rules Configuration';
      case 'Process Ownership':
        return 'Add Process Role';
      case 'Trigger Definition':
        return 'Add Trigger Definition';
      case 'Local Objectives':
        return 'Objective Management';
      case 'Pathway Definitions':
        return 'Add Pathway';
      case 'Pathway Detail':
        return 'Pathway Routing Configuration';
      case 'Business Rules':
        return 'Add Business Rule';
      case 'Validation Rules':
        return 'Validation Configuration';
      default:
        return 'Item';
    }
  }

  void _showAddAttributeModal(BuildContext context) {
    final attributeNameController = TextEditingController();
    final displayNameController = TextEditingController();
    String selectedDataType = 'string';
    String selectedRequired = 'NO';
    String selectedUnique = 'NO';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                height: MediaQuery.of(context).size.height * 0.85,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12, bottom: 8),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Modal content
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Modal header
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Add Attribute',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleLarge(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  icon: const Icon(Icons.close),
                                  iconSize: 24,
                                  color: Colors.grey[600],
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Scrollable form content
                            Expanded(
                              child: SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Attribute Name
                                    _buildMobileFormField(
                                        context,
                                        'Attribute Name',
                                        attributeNameController),
                                    const SizedBox(height: 16),

                                    // Display Name
                                    _buildMobileFormField(context,
                                        'Display Name', displayNameController),
                                    const SizedBox(height: 16),

                                    // Data Type
                                    _buildMobileDropdownField(
                                        context,
                                        'Data Type',
                                        ['string', 'number', 'boolean', 'date'],
                                        selectedDataType, (value) {
                                      setState(() {
                                        selectedDataType = value!;
                                      });
                                    }),
                                    const SizedBox(height: 16),

                                    // Required and Unique in a row
                                    Row(
                                      children: [
                                        Expanded(
                                          child: _buildMobileDropdownField(
                                              context,
                                              'Required',
                                              ['NO', 'YES'],
                                              selectedRequired, (value) {
                                            setState(() {
                                              selectedRequired = value!;
                                            });
                                          }),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: _buildMobileDropdownField(
                                              context,
                                              'Unique',
                                              ['NO', 'YES'],
                                              selectedUnique, (value) {
                                            setState(() {
                                              selectedUnique = value!;
                                            });
                                          }),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Action buttons - Fixed at bottom
                            Row(
                              children: [
                                Expanded(
                                  child: OutlinedButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    style: OutlinedButton.styleFrom(
                                      side:
                                          BorderSide(color: Colors.grey[300]!),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                    ),
                                    child: Text(
                                      'Cancel',
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                            ResponsiveFontSizes.bodyMedium(
                                                context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      if (attributeNameController
                                              .text.isNotEmpty &&
                                          displayNameController
                                              .text.isNotEmpty) {
                                        this.setState(() {
                                          _attributeData.add({
                                            'attributeName':
                                                attributeNameController.text,
                                            'displayName':
                                                displayNameController.text,
                                            'dataType': selectedDataType,
                                            'required': selectedRequired,
                                            'unique': selectedUnique,
                                          });
                                        });
                                        Navigator.of(context).pop();
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF0058FF),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                      elevation: 0,
                                    ),
                                    child: Text(
                                      'Add',
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                            ResponsiveFontSizes.bodyMedium(
                                                context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showEditAttributeModal(
      BuildContext context,
      int index,
      String attributeName,
      String displayName,
      String dataType,
      String required,
      String unique) {
    final attributeNameController = TextEditingController(text: attributeName);
    final displayNameController = TextEditingController(text: displayName);
    String selectedDataType = dataType;
    String selectedRequired = required;
    String selectedUnique = unique;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                height: MediaQuery.of(context).size.height * 0.85,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12, bottom: 8),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Modal content
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Modal header
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Edit Attribute',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleLarge(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  icon: const Icon(Icons.close),
                                  iconSize: 24,
                                  color: Colors.grey[600],
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Scrollable form content
                            Expanded(
                              child: SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Attribute Name
                                    _buildMobileFormField(
                                        context,
                                        'Attribute Name',
                                        attributeNameController),
                                    const SizedBox(height: 16),

                                    // Display Name
                                    _buildMobileFormField(context,
                                        'Display Name', displayNameController),
                                    const SizedBox(height: 16),

                                    // Data Type
                                    _buildMobileDropdownField(
                                        context,
                                        'Data Type',
                                        ['string', 'number', 'boolean', 'date'],
                                        selectedDataType, (value) {
                                      setState(() {
                                        selectedDataType = value!;
                                      });
                                    }),
                                    const SizedBox(height: 16),

                                    // Required and Unique in a row
                                    Row(
                                      children: [
                                        Expanded(
                                          child: _buildMobileDropdownField(
                                              context,
                                              'Required',
                                              ['NO', 'YES'],
                                              selectedRequired, (value) {
                                            setState(() {
                                              selectedRequired = value!;
                                            });
                                          }),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: _buildMobileDropdownField(
                                              context,
                                              'Unique',
                                              ['NO', 'YES'],
                                              selectedUnique, (value) {
                                            setState(() {
                                              selectedUnique = value!;
                                            });
                                          }),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Action buttons - Fixed at bottom
                            Row(
                              children: [
                                Expanded(
                                  child: OutlinedButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    style: OutlinedButton.styleFrom(
                                      side:
                                          BorderSide(color: Colors.grey[300]!),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                    ),
                                    child: Text(
                                      'Cancel',
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                            ResponsiveFontSizes.bodyMedium(
                                                context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _attributeData[index] = {
                                          'attributeName':
                                              attributeNameController.text,
                                          'displayName':
                                              displayNameController.text,
                                          'dataType': selectedDataType,
                                          'required': selectedRequired,
                                          'unique': selectedUnique,
                                        };
                                      });
                                      Navigator.of(context).pop();
                                      this.setState(() {});
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF0058FF),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                      elevation: 0,
                                    ),
                                    child: Text(
                                      'Update',
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                            ResponsiveFontSizes.bodyMedium(
                                                context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _deleteAttribute(BuildContext context, int index, String attributeName) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.4,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Modal content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Modal header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Delete Attribute',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.titleLarge(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 24,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Content
                        Expanded(
                          child: Text(
                            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[700],
                              height: 1.5,
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action buttons - Fixed at bottom
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(color: Colors.grey[300]!),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  setState(() {
                                    _attributeData.removeAt(index);
                                  });
                                  Navigator.of(context).pop();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red[600],
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Delete',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMobileFormField(
      BuildContext context, String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileDropdownField(BuildContext context, String label,
      List<String> options, String selectedValue, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 6),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            filled: true,
            fillColor: Colors.white,
          ),
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }
}

String _getModalTitle(String title) {
  switch (title) {
    case 'Object Details':
      return 'Add Object Property';
    case 'Entity Relationships':
      return 'Add Entity Relationship';
    case 'Attribute Business Rules':
      return 'Add Business Rule';
    case 'Enumerated Values':
      return 'Add Enumerated Value';
    case 'System Permissions':
      return 'Add System Permission';
    case 'Security Classification':
      return 'Add Security Classification';
    case 'Core Metadata':
      return 'Business Rules Configuration';
    case 'Process Ownership':
      return 'Add Process Role';
    case 'Trigger Definition':
      return 'Add Trigger Definition';
    case 'Local Objectives':
      return 'Objective Management';
    case 'Pathway Definitions':
      return 'Add Pathway';
    case 'Pathway Detail':
      return 'Pathway Routing Configuration';
    case 'Business Rules':
      return 'Add Business Rule';
    case 'Validation Rules':
      return 'Validation Configuration';
    default:
      return 'Add Configuration';
  }
}

List<List<String>> _getTableData(String title) {
  switch (title) {
    case 'Object Details':
      return [
        [
          'object_name',
          'Customer',
          'string',
          'YES',
          'Primary object identifier'
        ],
        [
          'created_date',
          '2024-01-15',
          'date',
          'YES',
          'Object creation timestamp'
        ],
        ['version', '1.0', 'string', 'NO', 'Object version number'],
      ];
    case 'Entity Relationships':
      return [
        ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
        ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
        ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
      ];
    case 'Attribute Business Rules':
      return [
        ['email_validation', 'email', 'format = email', 'reject', 'High'],
        ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
        ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
      ];
    case 'Enumerated Values':
      return [
        [
          'status_enum',
          'Active, Inactive, Pending',
          'Active',
          'Customer status options',
          'Active'
        ],
        [
          'type_enum',
          'Individual, Corporate',
          'Individual',
          'Customer type classification',
          'Active'
        ],
        [
          'priority_enum',
          'Low, Medium, High',
          'Medium',
          'Priority levels',
          'Active'
        ],
      ];
    case 'System Permissions':
      return [
        ['read_customer', 'User', 'Read', 'Customer Data', 'Active'],
        ['write_customer', 'Admin', 'Write', 'Customer Data', 'Active'],
        [
          'delete_customer',
          'SuperAdmin',
          'Delete',
          'Customer Data',
          'Restricted'
        ],
      ];
    case 'Security Classification':
      return [
        [
          'PII_Data',
          'High',
          'email, phone, address',
          'Encryption Required',
          'Active'
        ],
        ['Public_Data', 'Low', 'name, company', 'No Restrictions', 'Active'],
        [
          'Financial_Data',
          'Critical',
          'payment_info',
          'Strict Access Control',
          'Active'
        ],
      ];
    case 'Core Metadata':
      return [
        ['Name', 'Customer Onboardig', 'SOURCE'],
        ['Display Name', 'Customer Onboarding Process', 'SOURCE'],
        ['Primary Entity', 'Primary_Entity', 'SOURCE'],
      ];
    case 'Process Ownership':
      return [
        ['Originator', 'Role who initiates', 'In details'],
        ['Executive', 'Role responsible for', 'In details'],
        ['Business Sponsor', 'Department Role', 'In details'],
      ];
    case 'Trigger Definition':
      return [
        [
          'Originator',
          'Role who initiates',
          'trigger condition',
          'trigger schedule'
        ],
        [
          'Originator testing',
          'Role who finished',
          'trigger condition',
          'trigger schedule'
        ]
      ];
    case 'Local Objectives':
      return [
        ['LO-1', 'Validates Registration', 'agent type', 'In details'],
        ['LO-2', 'Validates Registration', 'agent type', 'In details'],
        ['LO-3', 'Validates Registration', 'agent type', 'In details'],
      ];
    case 'Pathway Definitions':
      return [
        ['Happy Path', 'Successfull Onbaord', 'types', 'description'],
        ['Happy Path', 'Successfull Onbaord', 'types', 'description']
      ];
    case 'Pathway Detail':
      return [
        [
          'ValidateRegistration',
          'Validate user reg',
          'routing condition',
          'description'
        ],
        [
          'ValidateRegistration',
          'Validate user reg',
          'routing condition',
          'description'
        ]
      ];
    case 'Business Rules':
      return [
        [
          'Executive',
          'Customer',
          'inputs',
          'description',
          'operation',
          'validation',
          'output',
          'error message'
        ],
        [
          'admin',
          'laber',
          'inputs',
          'description',
          'operation',
          'validation',
          'output',
          'error message'
        ]
      ];
    case 'Validation Rules':
      return [
        [
          'go_completences_check',
          'Validate G',
          'inputs',
          'description',
          'operation',
          'validation',
          'output',
          'error message'
        ]
      ];
    default:
      return [
        ['sample_name', 'sample_value', 'string', 'Active'],
        ['example_item', 'example_data', 'number', 'Pending'],
      ];
  }
}

List<Widget> _buildScrollableFormTableRows(BuildContext context, String title) {
  List<List<String>> rowsData = _getTableData(title);
  List<double> widths = _getColumnWidths(title);

  return rowsData.map((rowData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: rowData.asMap().entries.map((entry) {
          int colIndex = entry.key;
          String data = entry.value;
          double width = widths[colIndex];

          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                data,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }).toList();
}

List<double> _getColumnWidths(String title) {
  switch (title) {
    case 'Object Details':
      return [150, 100, 80, 100, 120];
    case 'Entity Relationships':
      return [220, 180, 140, 120, 110];
    case 'Attribute Business Rules':
      return [220, 150, 150, 120, 110];
    case 'Enumerated Values':
      return [150, 200, 100, 200, 100];
    case 'System Permissions':
      return [200, 150, 150, 150, 100];
    case 'Security Classification':
      return [150, 100, 200, 200, 100];
    case 'Core Metadata':
      return [200, 150, 150, 100];
    case 'Process Ownership':
      return [200, 150, 150, 100];
    case 'Trigger Definition':
      return [200, 150, 150, 150, 100];
    case 'Local Objectives':
      return [200, 150, 150, 100, 100];
    case 'Pathway Definitions':
      return [200, 150, 150, 100, 100];
    case 'Pathway Detail':
      return [200, 150, 150, 100, 100];
    case 'Business Rules':
      return [200, 150, 150, 150, 150, 150, 100, 100, 100];
    case 'Validation Rules':
      return [200, 150, 150, 150, 150, 150, 100, 100, 100];
    default:
      return [120, 120, 100, 100];
  }
}

List<String> _getTableHeaders(String title) {
  switch (title) {
    case 'Object Details':
      return ['PROPERTY NAME', 'VALUE', 'TYPE', 'REQUIRED', 'DESCRIPTION'];
    case 'Entity Relationships':
      return [
        'RELATIONSHIP NAME',
        'TARGET ENTITY',
        'TYPE',
        'CARDINALITY',
        'STATUS'
      ];
    case 'Attribute Business Rules':
      return ['RULE NAME', 'ATTRIBUTE', 'CONDITION', 'ACTION', 'PRIORITY'];
    case 'Enumerated Values':
      return ['ENUM NAME', 'VALUES', 'DEFAULT', 'DESCRIPTION', 'STATUS'];
    case 'System Permissions':
      return ['PERMISSION NAME', 'ROLE', 'ACCESS LEVEL', 'RESOURCE', 'STATUS'];
    case 'Security Classification':
      return [
        'CLASSIFICATION',
        'LEVEL',
        'ATTRIBUTES',
        'RESTRICTIONS',
        'STATUS'
      ];
    case 'Core Metadata':
      return ['FIELD', 'VALUE', 'SOURCE'];
    case 'Process Ownership':
      return ['ROLE TYPE', 'ASSIGNED ROLE', 'DESCRIPTION'];
    case 'Trigger Definition':
      return [
        'TRIGGER TYPE',
        'TRIGGER ATTRIBUTES',
        'TRIGGER CONDITION',
        'TRIGGER SCHEDULE'
      ];
    case 'Local Objectives':
      return ['LO ID', 'OBJECTIVE NAME', 'AGENT TYPE', 'DESCRIPTION'];
    case 'Pathway Definitions':
      return ['PATHWAY NAME', 'STEPS', 'TYPE', 'DESCRIPTION'];
    case 'Pathway Detail':
      return ['OBJECTIVE', 'ROUTE TYPE', 'ROUTING CONDITIONS', 'DESCRIPTION'];
    case 'Business Rules':
      return [
        'ROLE TYPE',
        'ENTITY',
        'INPUTS',
        'DESCRIPTION',
        'OPERATION',
        'VALIDATION',
        'OUTPUT',
        'ERROR MESSAGE'
      ];
    case 'Validation Rules':
      return [
        'RULE NAME',
        'ENTITY',
        'INPUTS',
        'DESCRIPTION',
        'OPERATION',
        'VALIDATION',
        'OUTPUT',
        'ERROR MESSAGE'
      ];
    default:
      return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
  }
}

Widget _buildModalTextAreaFieldObjectFlow(
    BuildContext context, String label, String hint) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        label,
        style: FontManager.getCustomStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Color(0xFF242424),
        ),
      ),
      const SizedBox(height: 8),
      TextFormField(
        maxLines: 4,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: FontManager.getCustomStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF0058FF)),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          filled: true,
          fillColor: Colors.white,
        ),
        style: FontManager.getCustomStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      ),
    ],
  );
}
