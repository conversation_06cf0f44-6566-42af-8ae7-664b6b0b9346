class GetAllMylibrarylist {
  bool? success;
  String? message;
  Data? data;
  dynamic error;
  DateTime? timestamp;

  GetAllMylibrarylist({
    this.success,
    this.message,
    this.data,
    this.error,
    this.timestamp,
  });

  GetAllMylibrarylist copyWith({
    bool? success,
    String? message,
    Data? data,
    dynamic error,
    DateTime? timestamp,
  }) =>
      GetAllMylibrarylist(
        success: success ?? this.success,
        message: message ?? this.message,
        data: data ?? this.data,
        error: error ?? this.error,
        timestamp: timestamp ?? this.timestamp,
      );
}

class Data {
  String? tenantId;
  Library? dataLibrary;
  Summary? summary;
  Metadata? metadata;

  Data({
    this.tenantId,
    this.dataLibrary,
    this.summary,
    this.metadata,
  });

  Data copyWith({
    String? tenantId,
    Library? dataLibrary,
    Summary? summary,
    Metadata? metadata,
  }) =>
      Data(
        tenantId: tenantId ?? this.tenantId,
        dataLibrary: dataLibrary ?? this.dataLibrary,
        summary: summary ?? this.summary,
        metadata: metadata ?? this.metadata,
      );
}

class Library {
  TenantInfo? tenantInfo;
  Roles? roles;
  Departments? departments;
  BusinessRules? roleInheritance;
  Entities? entities;
  EntityAttributes? entityAttributes;
  AttributeValidations? attributeValidations;
  EntityRelationships? entityRelationships;
  AttributeValidations? uiProperties;
  SecurityProperties? securityProperties;
  AttributeValidations? constants;
  BusinessRules? businessRules;
  SystemPermissions? systemPermissions;
  GlobalObjectives? globalObjectives;
  LocalObjectives? localObjectives;

  Library({
    this.tenantInfo,
    this.roles,
    this.departments,
    this.roleInheritance,
    this.entities,
    this.entityAttributes,
    this.attributeValidations,
    this.entityRelationships,
    this.uiProperties,
    this.securityProperties,
    this.constants,
    this.businessRules,
    this.systemPermissions,
    this.globalObjectives,
    this.localObjectives,
  });

  Library copyWith({
    TenantInfo? tenantInfo,
    Roles? roles,
    Departments? departments,
    BusinessRules? roleInheritance,
    Entities? entities,
    EntityAttributes? entityAttributes,
    AttributeValidations? attributeValidations,
    EntityRelationships? entityRelationships,
    AttributeValidations? uiProperties,
    SecurityProperties? securityProperties,
    AttributeValidations? constants,
    BusinessRules? businessRules,
    SystemPermissions? systemPermissions,
    GlobalObjectives? globalObjectives,
    LocalObjectives? localObjectives,
  }) =>
      Library(
        tenantInfo: tenantInfo ?? this.tenantInfo,
        roles: roles ?? this.roles,
        departments: departments ?? this.departments,
        roleInheritance: roleInheritance ?? this.roleInheritance,
        entities: entities ?? this.entities,
        entityAttributes: entityAttributes ?? this.entityAttributes,
        attributeValidations: attributeValidations ?? this.attributeValidations,
        entityRelationships: entityRelationships ?? this.entityRelationships,
        uiProperties: uiProperties ?? this.uiProperties,
        securityProperties: securityProperties ?? this.securityProperties,
        constants: constants ?? this.constants,
        businessRules: businessRules ?? this.businessRules,
        systemPermissions: systemPermissions ?? this.systemPermissions,
        globalObjectives: globalObjectives ?? this.globalObjectives,
        localObjectives: localObjectives ?? this.localObjectives,
      );
}

class AttributeValidations {
  List<AttributeValidationsPostgre>? postgres;
  List<dynamic>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  AttributeValidations({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  AttributeValidations copyWith({
    List<AttributeValidationsPostgre>? postgres,
    List<dynamic>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      AttributeValidations(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class AttributeValidationsPostgre {
  String? attributeId;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? errorMessage;
  int? validationId;
  int? id;
  String? createdBy;
  String? updatedBy;
  String? leftOperand;
  String? postgreOperator;
  String? rightOperand;
  String? successValue;
  String? warningValue;
  String? failureValue;
  String? multiConditionOperator;
  String? warningMessage;
  String? successMessage;
  String? naturalLanguage;
  int? version;
  String? status;

  AttributeValidationsPostgre({
    this.attributeId,
    this.createdAt,
    this.updatedAt,
    this.errorMessage,
    this.validationId,
    this.id,
    this.createdBy,
    this.updatedBy,
    this.leftOperand,
    this.postgreOperator,
    this.rightOperand,
    this.successValue,
    this.warningValue,
    this.failureValue,
    this.multiConditionOperator,
    this.warningMessage,
    this.successMessage,
    this.naturalLanguage,
    this.version,
    this.status,
  });

  AttributeValidationsPostgre copyWith({
    String? attributeId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? errorMessage,
    int? validationId,
    int? id,
    String? createdBy,
    String? updatedBy,
    String? leftOperand,
    String? postgreOperator,
    String? rightOperand,
    String? successValue,
    String? warningValue,
    String? failureValue,
    String? multiConditionOperator,
    String? warningMessage,
    String? successMessage,
    String? naturalLanguage,
    int? version,
    String? status,
  }) =>
      AttributeValidationsPostgre(
        attributeId: attributeId ?? this.attributeId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        errorMessage: errorMessage ?? this.errorMessage,
        validationId: validationId ?? this.validationId,
        id: id ?? this.id,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        leftOperand: leftOperand ?? this.leftOperand,
        postgreOperator: postgreOperator ?? this.postgreOperator,
        rightOperand: rightOperand ?? this.rightOperand,
        successValue: successValue ?? this.successValue,
        warningValue: warningValue ?? this.warningValue,
        failureValue: failureValue ?? this.failureValue,
        multiConditionOperator:
            multiConditionOperator ?? this.multiConditionOperator,
        warningMessage: warningMessage ?? this.warningMessage,
        successMessage: successMessage ?? this.successMessage,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
      );
}

class BusinessRules {
  List<BusinessRulesPostgre>? postgres;
  List<BusinessRulesMongoDraft>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  BusinessRules({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  BusinessRules copyWith({
    List<BusinessRulesPostgre>? postgres,
    List<BusinessRulesMongoDraft>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      BusinessRules(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class BusinessRulesMongoDraft {
  String? id;
  int? mongoDraftId;
  String? parentRoleId;
  String? inheritsRoleId;
  String? naturalLanguage;
  int? version;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;

  BusinessRulesMongoDraft({
    this.id,
    this.mongoDraftId,
    this.parentRoleId,
    this.inheritsRoleId,
    this.naturalLanguage,
    this.version,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  BusinessRulesMongoDraft copyWith({
    String? id,
    int? mongoDraftId,
    String? parentRoleId,
    String? inheritsRoleId,
    String? naturalLanguage,
    int? version,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      BusinessRulesMongoDraft(
        id: id ?? this.id,
        mongoDraftId: mongoDraftId ?? this.mongoDraftId,
        parentRoleId: parentRoleId ?? this.parentRoleId,
        inheritsRoleId: inheritsRoleId ?? this.inheritsRoleId,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
}

class BusinessRulesPostgre {
  String? attributeId;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? errorMessage;
  int? validationId;
  int? id;
  String? createdBy;
  String? updatedBy;
  String? leftOperand;
  String? postgreOperator;
  String? rightOperand;
  String? successValue;
  String? warningValue;
  String? failureValue;
  String? multiConditionOperator;
  String? warningMessage;
  String? successMessage;
  String? naturalLanguage;
  int? version;
  String? status;
  String? entityId;
  String? name;
  String? displayName;
  String? datatype;
  bool? isPrimaryKey;
  bool? isForeignKey;
  bool? isRequired;
  bool? isUnique;
  String? defaultValue;
  String? description;
  bool? isCalculated;
  String? calculationFormula;
  bool? required;
  bool? calculatedField;
  String? defaultType;
  String? helperText;
  String? type;
  String? tableName;
  String? tenantId;
  String? tenantName;
  String? businessDomain;
  String? category;
  List<String>? tags;
  String? archivalStrategy;
  String? icon;
  String? colourTheme;

  BusinessRulesPostgre({
    this.attributeId,
    this.createdAt,
    this.updatedAt,
    this.errorMessage,
    this.validationId,
    this.id,
    this.createdBy,
    this.updatedBy,
    this.leftOperand,
    this.postgreOperator,
    this.rightOperand,
    this.successValue,
    this.warningValue,
    this.failureValue,
    this.multiConditionOperator,
    this.warningMessage,
    this.successMessage,
    this.naturalLanguage,
    this.version,
    this.status,
    this.entityId,
    this.name,
    this.displayName,
    this.datatype,
    this.isPrimaryKey,
    this.isForeignKey,
    this.isRequired,
    this.isUnique,
    this.defaultValue,
    this.description,
    this.isCalculated,
    this.calculationFormula,
    this.required,
    this.calculatedField,
    this.defaultType,
    this.helperText,
    this.type,
    this.tableName,
    this.tenantId,
    this.tenantName,
    this.businessDomain,
    this.category,
    this.tags,
    this.archivalStrategy,
    this.icon,
    this.colourTheme,
  });

  BusinessRulesPostgre copyWith({
    String? attributeId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? errorMessage,
    int? validationId,
    int? id,
    String? createdBy,
    String? updatedBy,
    String? leftOperand,
    String? postgreOperator,
    String? rightOperand,
    String? successValue,
    String? warningValue,
    String? failureValue,
    String? multiConditionOperator,
    String? warningMessage,
    String? successMessage,
    String? naturalLanguage,
    int? version,
    String? status,
    String? entityId,
    String? name,
    String? displayName,
    String? datatype,
    bool? isPrimaryKey,
    bool? isForeignKey,
    bool? isRequired,
    bool? isUnique,
    String? defaultValue,
    String? description,
    bool? isCalculated,
    String? calculationFormula,
    bool? required,
    bool? calculatedField,
    String? defaultType,
    String? helperText,
    String? type,
    String? tableName,
    String? tenantId,
    String? tenantName,
    String? businessDomain,
    String? category,
    List<String>? tags,
    String? archivalStrategy,
    String? icon,
    String? colourTheme,
  }) =>
      BusinessRulesPostgre(
        attributeId: attributeId ?? this.attributeId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        errorMessage: errorMessage ?? this.errorMessage,
        validationId: validationId ?? this.validationId,
        id: id ?? this.id,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        leftOperand: leftOperand ?? this.leftOperand,
        postgreOperator: postgreOperator ?? this.postgreOperator,
        rightOperand: rightOperand ?? this.rightOperand,
        successValue: successValue ?? this.successValue,
        warningValue: warningValue ?? this.warningValue,
        failureValue: failureValue ?? this.failureValue,
        multiConditionOperator:
            multiConditionOperator ?? this.multiConditionOperator,
        warningMessage: warningMessage ?? this.warningMessage,
        successMessage: successMessage ?? this.successMessage,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
        entityId: entityId ?? this.entityId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        datatype: datatype ?? this.datatype,
        isPrimaryKey: isPrimaryKey ?? this.isPrimaryKey,
        isForeignKey: isForeignKey ?? this.isForeignKey,
        isRequired: isRequired ?? this.isRequired,
        isUnique: isUnique ?? this.isUnique,
        defaultValue: defaultValue ?? this.defaultValue,
        description: description ?? this.description,
        isCalculated: isCalculated ?? this.isCalculated,
        calculationFormula: calculationFormula ?? this.calculationFormula,
        required: required ?? this.required,
        calculatedField: calculatedField ?? this.calculatedField,
        defaultType: defaultType ?? this.defaultType,
        helperText: helperText ?? this.helperText,
        type: type ?? this.type,
        tableName: tableName ?? this.tableName,
        tenantId: tenantId ?? this.tenantId,
        tenantName: tenantName ?? this.tenantName,
        businessDomain: businessDomain ?? this.businessDomain,
        category: category ?? this.category,
        tags: tags ?? this.tags,
        archivalStrategy: archivalStrategy ?? this.archivalStrategy,
        icon: icon ?? this.icon,
        colourTheme: colourTheme ?? this.colourTheme,
      );
}

class Departments {
  List<DepartmentsPostgre>? postgres;
  List<PostgresRecordElement>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  Departments({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  Departments copyWith({
    List<DepartmentsPostgre>? postgres,
    List<PostgresRecordElement>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      Departments(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class PostgresRecordElement {
  String? id;
  int? mongoDraftId;
  String? tenantId;
  String? name;
  String? description;
  dynamic departmentHeadRoleId;
  int? parentDepartmentId;
  String? naturalLanguage;
  DateTime? createdAt;
  String? createdBy;
  DateTime? updatedAt;
  String? updatedBy;
  int? version;
  String? status;

  PostgresRecordElement({
    this.id,
    this.mongoDraftId,
    this.tenantId,
    this.name,
    this.description,
    this.departmentHeadRoleId,
    this.parentDepartmentId,
    this.naturalLanguage,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.version,
    this.status,
  });

  PostgresRecordElement copyWith({
    String? id,
    int? mongoDraftId,
    String? tenantId,
    String? name,
    String? description,
    dynamic departmentHeadRoleId,
    int? parentDepartmentId,
    String? naturalLanguage,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    int? version,
    String? status,
  }) =>
      PostgresRecordElement(
        id: id ?? this.id,
        mongoDraftId: mongoDraftId ?? this.mongoDraftId,
        tenantId: tenantId ?? this.tenantId,
        name: name ?? this.name,
        description: description ?? this.description,
        departmentHeadRoleId: departmentHeadRoleId ?? this.departmentHeadRoleId,
        parentDepartmentId: parentDepartmentId ?? this.parentDepartmentId,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        version: version ?? this.version,
        status: status ?? this.status,
      );
}

class DepartmentsPostgre {
  String? departmentId;
  String? name;
  String? description;
  dynamic departmentHeadRoleId;
  String? parentDepartmentId;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  int? id;
  String? tenantId;
  String? naturalLanguage;
  int? version;

  DepartmentsPostgre({
    this.departmentId,
    this.name,
    this.description,
    this.departmentHeadRoleId,
    this.parentDepartmentId,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.id,
    this.tenantId,
    this.naturalLanguage,
    this.version,
  });

  DepartmentsPostgre copyWith({
    String? departmentId,
    String? name,
    String? description,
    dynamic departmentHeadRoleId,
    String? parentDepartmentId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    int? id,
    String? tenantId,
    String? naturalLanguage,
    int? version,
  }) =>
      DepartmentsPostgre(
        departmentId: departmentId ?? this.departmentId,
        name: name ?? this.name,
        description: description ?? this.description,
        departmentHeadRoleId: departmentHeadRoleId ?? this.departmentHeadRoleId,
        parentDepartmentId: parentDepartmentId ?? this.parentDepartmentId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        id: id ?? this.id,
        tenantId: tenantId ?? this.tenantId,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
      );
}

class Entities {
  List<EntitiesMongoDraft>? postgres;
  List<EntitiesMongoDraft>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  Entities({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  Entities copyWith({
    List<EntitiesMongoDraft>? postgres,
    List<EntitiesMongoDraft>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      Entities(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class EntitiesMongoDraft {
  String? id;
  String? entityId;
  String? name;
  String? displayName;
  String? tenantId;
  String? tenantName;
  String? businessDomain;
  String? category;
  List<String>? tags;
  String? archivalStrategy;
  String? icon;
  String? colourTheme;
  int? version;
  String? status;
  String? type;
  String? description;
  String? tableName;
  String? naturalLanguage;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? entityStatus;
  List<dynamic>? changesDetected;
  String? iconType;
  String? iconContent;

  EntitiesMongoDraft({
    this.id,
    this.entityId,
    this.name,
    this.displayName,
    this.tenantId,
    this.tenantName,
    this.businessDomain,
    this.category,
    this.tags,
    this.archivalStrategy,
    this.icon,
    this.colourTheme,
    this.version,
    this.status,
    this.type,
    this.description,
    this.tableName,
    this.naturalLanguage,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.entityStatus,
    this.changesDetected,
    this.iconType,
    this.iconContent,
  });

  EntitiesMongoDraft copyWith({
    String? id,
    String? entityId,
    String? name,
    String? displayName,
    String? tenantId,
    String? tenantName,
    String? businessDomain,
    String? category,
    List<String>? tags,
    String? archivalStrategy,
    String? icon,
    String? colourTheme,
    int? version,
    String? status,
    String? type,
    String? description,
    String? tableName,
    String? naturalLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? entityStatus,
    List<dynamic>? changesDetected,
    String? iconType,
    String? iconContent,
  }) =>
      EntitiesMongoDraft(
        id: id ?? this.id,
        entityId: entityId ?? this.entityId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        tenantId: tenantId ?? this.tenantId,
        tenantName: tenantName ?? this.tenantName,
        businessDomain: businessDomain ?? this.businessDomain,
        category: category ?? this.category,
        tags: tags ?? this.tags,
        archivalStrategy: archivalStrategy ?? this.archivalStrategy,
        icon: icon ?? this.icon,
        colourTheme: colourTheme ?? this.colourTheme,
        version: version ?? this.version,
        status: status ?? this.status,
        type: type ?? this.type,
        description: description ?? this.description,
        tableName: tableName ?? this.tableName,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        entityStatus: entityStatus ?? this.entityStatus,
        changesDetected: changesDetected ?? this.changesDetected,
        iconType: iconType ?? this.iconType,
        iconContent: iconContent ?? this.iconContent,
      );
}

class EntityAttributes {
  List<EntityAttributesMongoDraft>? postgres;
  List<EntityAttributesMongoDraft>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  EntityAttributes({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  EntityAttributes copyWith({
    List<EntityAttributesMongoDraft>? postgres,
    List<EntityAttributesMongoDraft>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      EntityAttributes(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class EntityAttributesMongoDraft {
  String? id;
  String? attributeId;
  String? entityId;
  String? tenantId;
  String? name;
  String? displayName;
  String? datatype;
  bool? isPrimaryKey;
  bool? isForeignKey;
  bool? isRequired;
  bool? isUnique;
  String? defaultType;
  String? defaultValue;
  String? description;
  String? helperText;
  bool? isCalculated;
  String? calculationFormula;
  int? version;
  String? status;
  String? naturalLanguage;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  bool? required;
  bool? calculatedField;

  EntityAttributesMongoDraft({
    this.id,
    this.attributeId,
    this.entityId,
    this.tenantId,
    this.name,
    this.displayName,
    this.datatype,
    this.isPrimaryKey,
    this.isForeignKey,
    this.isRequired,
    this.isUnique,
    this.defaultType,
    this.defaultValue,
    this.description,
    this.helperText,
    this.isCalculated,
    this.calculationFormula,
    this.version,
    this.status,
    this.naturalLanguage,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.required,
    this.calculatedField,
  });

  EntityAttributesMongoDraft copyWith({
    String? id,
    String? attributeId,
    String? entityId,
    String? tenantId,
    String? name,
    String? displayName,
    String? datatype,
    bool? isPrimaryKey,
    bool? isForeignKey,
    bool? isRequired,
    bool? isUnique,
    String? defaultType,
    String? defaultValue,
    String? description,
    String? helperText,
    bool? isCalculated,
    String? calculationFormula,
    int? version,
    String? status,
    String? naturalLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? required,
    bool? calculatedField,
  }) =>
      EntityAttributesMongoDraft(
        id: id ?? this.id,
        attributeId: attributeId ?? this.attributeId,
        entityId: entityId ?? this.entityId,
        tenantId: tenantId ?? this.tenantId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        datatype: datatype ?? this.datatype,
        isPrimaryKey: isPrimaryKey ?? this.isPrimaryKey,
        isForeignKey: isForeignKey ?? this.isForeignKey,
        isRequired: isRequired ?? this.isRequired,
        isUnique: isUnique ?? this.isUnique,
        defaultType: defaultType ?? this.defaultType,
        defaultValue: defaultValue ?? this.defaultValue,
        description: description ?? this.description,
        helperText: helperText ?? this.helperText,
        isCalculated: isCalculated ?? this.isCalculated,
        calculationFormula: calculationFormula ?? this.calculationFormula,
        version: version ?? this.version,
        status: status ?? this.status,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        required: required ?? this.required,
        calculatedField: calculatedField ?? this.calculatedField,
      );
}

class EntityRelationships {
  List<EntityRelationshipsPostgre>? postgres;
  List<dynamic>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  EntityRelationships({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  EntityRelationships copyWith({
    List<EntityRelationshipsPostgre>? postgres,
    List<dynamic>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      EntityRelationships(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class EntityRelationshipsPostgre {
  int? relationshipId;
  String? sourceEntityId;
  String? targetEntityId;
  String? relationshipType;
  String? sourceAttributeId;
  String? targetAttributeId;
  String? onDelete;
  String? onUpdate;
  String? foreignKeyType;
  String? description;
  String? version;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;

  EntityRelationshipsPostgre({
    this.relationshipId,
    this.sourceEntityId,
    this.targetEntityId,
    this.relationshipType,
    this.sourceAttributeId,
    this.targetAttributeId,
    this.onDelete,
    this.onUpdate,
    this.foreignKeyType,
    this.description,
    this.version,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  EntityRelationshipsPostgre copyWith({
    int? relationshipId,
    String? sourceEntityId,
    String? targetEntityId,
    String? relationshipType,
    String? sourceAttributeId,
    String? targetAttributeId,
    String? onDelete,
    String? onUpdate,
    String? foreignKeyType,
    String? description,
    String? version,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) =>
      EntityRelationshipsPostgre(
        relationshipId: relationshipId ?? this.relationshipId,
        sourceEntityId: sourceEntityId ?? this.sourceEntityId,
        targetEntityId: targetEntityId ?? this.targetEntityId,
        relationshipType: relationshipType ?? this.relationshipType,
        sourceAttributeId: sourceAttributeId ?? this.sourceAttributeId,
        targetAttributeId: targetAttributeId ?? this.targetAttributeId,
        onDelete: onDelete ?? this.onDelete,
        onUpdate: onUpdate ?? this.onUpdate,
        foreignKeyType: foreignKeyType ?? this.foreignKeyType,
        description: description ?? this.description,
        version: version ?? this.version,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
      );
}

class GlobalObjectives {
  List<GlobalObjectivesPostgre>? postgres;
  List<dynamic>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  GlobalObjectives({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  GlobalObjectives copyWith({
    List<GlobalObjectivesPostgre>? postgres,
    List<dynamic>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      GlobalObjectives(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class GlobalObjectivesPostgre {
  String? goId;
  String? name;
  String? version;
  String? status;
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? tenantId;
  DateTime? lastUsed;
  bool? deletedMark;
  String? versionType;
  dynamic metadata;
  int? autoId;
  String? primaryEntity;
  String? classification;
  String? tenantName;
  String? bookId;
  String? bookName;
  String? chapterId;
  String? chapterName;
  String? createdBy;
  String? updatedBy;
  String? naturalLanguage;

  GlobalObjectivesPostgre({
    this.goId,
    this.name,
    this.version,
    this.status,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.tenantId,
    this.lastUsed,
    this.deletedMark,
    this.versionType,
    this.metadata,
    this.autoId,
    this.primaryEntity,
    this.classification,
    this.tenantName,
    this.bookId,
    this.bookName,
    this.chapterId,
    this.chapterName,
    this.createdBy,
    this.updatedBy,
    this.naturalLanguage,
  });

  GlobalObjectivesPostgre copyWith({
    String? goId,
    String? name,
    String? version,
    String? status,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? tenantId,
    DateTime? lastUsed,
    bool? deletedMark,
    String? versionType,
    dynamic metadata,
    int? autoId,
    String? primaryEntity,
    String? classification,
    String? tenantName,
    String? bookId,
    String? bookName,
    String? chapterId,
    String? chapterName,
    String? createdBy,
    String? updatedBy,
    String? naturalLanguage,
  }) =>
      GlobalObjectivesPostgre(
        goId: goId ?? this.goId,
        name: name ?? this.name,
        version: version ?? this.version,
        status: status ?? this.status,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        tenantId: tenantId ?? this.tenantId,
        lastUsed: lastUsed ?? this.lastUsed,
        deletedMark: deletedMark ?? this.deletedMark,
        versionType: versionType ?? this.versionType,
        metadata: metadata ?? this.metadata,
        autoId: autoId ?? this.autoId,
        primaryEntity: primaryEntity ?? this.primaryEntity,
        classification: classification ?? this.classification,
        tenantName: tenantName ?? this.tenantName,
        bookId: bookId ?? this.bookId,
        bookName: bookName ?? this.bookName,
        chapterId: chapterId ?? this.chapterId,
        chapterName: chapterName ?? this.chapterName,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );
}

class LocalObjectives {
  List<LocalObjectivesPostgre>? postgres;
  List<LocalObjectivesPostgre>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  LocalObjectives({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  LocalObjectives copyWith({
    List<LocalObjectivesPostgre>? postgres,
    List<LocalObjectivesPostgre>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      LocalObjectives(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class LocalObjectivesPostgre {
  String? loId;
  String? name;
  String? functionType;
  String? goId;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? status;
  String? version;
  String? description;
  String? naturalLanguage;
  String? workflowSource;
  dynamic uiStack;
  dynamic mappingStack;
  String? versionType;
  int? autoId;
  String? agentType;
  String? executionRights;
  dynamic createdBy;
  String? updatedBy;
  bool? terminal;
  String? tableName;
  String? goName;
  String? tenantId;

  LocalObjectivesPostgre({
    this.loId,
    this.name,
    this.functionType,
    this.goId,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.version,
    this.description,
    this.naturalLanguage,
    this.workflowSource,
    this.uiStack,
    this.mappingStack,
    this.versionType,
    this.autoId,
    this.agentType,
    this.executionRights,
    this.createdBy,
    this.updatedBy,
    this.terminal,
    this.tableName,
    this.goName,
    this.tenantId,
  });

  LocalObjectivesPostgre copyWith({
    String? loId,
    String? name,
    String? functionType,
    String? goId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? status,
    String? version,
    String? description,
    String? naturalLanguage,
    String? workflowSource,
    dynamic uiStack,
    dynamic mappingStack,
    String? versionType,
    int? autoId,
    String? agentType,
    String? executionRights,
    dynamic createdBy,
    String? updatedBy,
    bool? terminal,
    String? tableName,
    String? goName,
    String? tenantId,
  }) =>
      LocalObjectivesPostgre(
        loId: loId ?? this.loId,
        name: name ?? this.name,
        functionType: functionType ?? this.functionType,
        goId: goId ?? this.goId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        status: status ?? this.status,
        version: version ?? this.version,
        description: description ?? this.description,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        workflowSource: workflowSource ?? this.workflowSource,
        uiStack: uiStack ?? this.uiStack,
        mappingStack: mappingStack ?? this.mappingStack,
        versionType: versionType ?? this.versionType,
        autoId: autoId ?? this.autoId,
        agentType: agentType ?? this.agentType,
        executionRights: executionRights ?? this.executionRights,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        terminal: terminal ?? this.terminal,
        tableName: tableName ?? this.tableName,
        goName: goName ?? this.goName,
        tenantId: tenantId ?? this.tenantId,
      );
}

class Roles {
  List<RolesPostgre>? postgres;
  List<RolesPostgre>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  Roles({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  Roles copyWith({
    List<RolesPostgre>? postgres,
    List<RolesPostgre>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      Roles(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class RolesPostgre {
  String? roleId;
  String? name;
  String? tenantId;
  dynamic inheritsFrom;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? description;
  dynamic permissions;
  dynamic scope;
  dynamic classification;
  dynamic specialConditions;
  String? versionType;
  int? id;
  String? reportsToRoleId;
  String? organizationalLevel;
  int? departmentId;
  String? naturalLanguage;
  int? version;
  String? createdBy;
  String? updatedBy;
  String? status;

  RolesPostgre({
    this.roleId,
    this.name,
    this.tenantId,
    this.inheritsFrom,
    this.createdAt,
    this.updatedAt,
    this.description,
    this.permissions,
    this.scope,
    this.classification,
    this.specialConditions,
    this.versionType,
    this.id,
    this.reportsToRoleId,
    this.organizationalLevel,
    this.departmentId,
    this.naturalLanguage,
    this.version,
    this.createdBy,
    this.updatedBy,
    this.status,
  });

  RolesPostgre copyWith({
    String? roleId,
    String? name,
    String? tenantId,
    dynamic inheritsFrom,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
    dynamic permissions,
    dynamic scope,
    dynamic classification,
    dynamic specialConditions,
    String? versionType,
    int? id,
    String? reportsToRoleId,
    String? organizationalLevel,
    int? departmentId,
    String? naturalLanguage,
    int? version,
    String? createdBy,
    String? updatedBy,
    String? status,
  }) =>
      RolesPostgre(
          roleId: roleId ?? this.roleId,
          name: name ?? this.name,
          tenantId: tenantId ?? this.tenantId,
          inheritsFrom: inheritsFrom ?? this.inheritsFrom,
          createdAt: createdAt ?? this.createdAt,
          updatedAt: updatedAt ?? this.updatedAt,
          description: description ?? this.description,
          permissions: permissions ?? this.permissions,
          scope: scope ?? this.scope,
          classification: classification ?? this.classification,
          specialConditions: specialConditions ?? this.specialConditions,
          versionType: versionType ?? this.versionType,
          id: id ?? this.id,
          reportsToRoleId: reportsToRoleId ?? this.reportsToRoleId,
          organizationalLevel: organizationalLevel ?? this.organizationalLevel,
          departmentId: departmentId ?? this.departmentId,
          naturalLanguage: naturalLanguage ?? this.naturalLanguage,
          version: version ?? this.version,
          createdBy: createdBy ?? this.createdBy,
          updatedBy: updatedBy ?? this.updatedBy,
          status: status ?? this.status);
}

class SecurityProperties {
  List<SecurityPropertiesPostgre>? postgres;
  List<dynamic>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  SecurityProperties({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  SecurityProperties copyWith({
    List<SecurityPropertiesPostgre>? postgres,
    List<dynamic>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      SecurityProperties(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class SecurityPropertiesPostgre {
  int? id;
  String? securityPropertyId;
  String? entityId;
  String? attributeId;
  String? classification;
  String? piiType;
  bool? encryptionRequired;
  String? encryptionType;
  bool? maskingRequired;
  String? maskingPattern;
  String? accessLevel;
  bool? auditTrail;
  String? dataResidency;
  String? retentionOverride;
  bool? anonymizationRequired;
  String? anonymizationMethod;
  String? complianceFrameworks;
  String? dataPurpose;
  bool? consentRequired;
  bool? crossBorderTransfer;
  bool? backupEncryption;
  bool? secureDeletion;
  String? naturalLanguage;
  int? version;
  String? status;
  String? createdBy;
  String? updatedBy;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? securityPropertyStatus;
  List<dynamic>? changesDetected;

  SecurityPropertiesPostgre({
    this.id,
    this.securityPropertyId,
    this.entityId,
    this.attributeId,
    this.classification,
    this.piiType,
    this.encryptionRequired,
    this.encryptionType,
    this.maskingRequired,
    this.maskingPattern,
    this.accessLevel,
    this.auditTrail,
    this.dataResidency,
    this.retentionOverride,
    this.anonymizationRequired,
    this.anonymizationMethod,
    this.complianceFrameworks,
    this.dataPurpose,
    this.consentRequired,
    this.crossBorderTransfer,
    this.backupEncryption,
    this.secureDeletion,
    this.naturalLanguage,
    this.version,
    this.status,
    this.createdBy,
    this.updatedBy,
    this.createdAt,
    this.updatedAt,
    this.securityPropertyStatus,
    this.changesDetected,
  });

  SecurityPropertiesPostgre copyWith({
    int? id,
    String? securityPropertyId,
    String? entityId,
    String? attributeId,
    String? classification,
    String? piiType,
    bool? encryptionRequired,
    String? encryptionType,
    bool? maskingRequired,
    String? maskingPattern,
    String? accessLevel,
    bool? auditTrail,
    String? dataResidency,
    String? retentionOverride,
    bool? anonymizationRequired,
    String? anonymizationMethod,
    String? complianceFrameworks,
    String? dataPurpose,
    bool? consentRequired,
    bool? crossBorderTransfer,
    bool? backupEncryption,
    bool? secureDeletion,
    String? naturalLanguage,
    int? version,
    String? status,
    String? createdBy,
    String? updatedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? securityPropertyStatus,
    List<dynamic>? changesDetected,
  }) =>
      SecurityPropertiesPostgre(
        id: id ?? this.id,
        securityPropertyId: securityPropertyId ?? this.securityPropertyId,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        classification: classification ?? this.classification,
        piiType: piiType ?? this.piiType,
        encryptionRequired: encryptionRequired ?? this.encryptionRequired,
        encryptionType: encryptionType ?? this.encryptionType,
        maskingRequired: maskingRequired ?? this.maskingRequired,
        maskingPattern: maskingPattern ?? this.maskingPattern,
        accessLevel: accessLevel ?? this.accessLevel,
        auditTrail: auditTrail ?? this.auditTrail,
        dataResidency: dataResidency ?? this.dataResidency,
        retentionOverride: retentionOverride ?? this.retentionOverride,
        anonymizationRequired:
            anonymizationRequired ?? this.anonymizationRequired,
        anonymizationMethod: anonymizationMethod ?? this.anonymizationMethod,
        complianceFrameworks: complianceFrameworks ?? this.complianceFrameworks,
        dataPurpose: dataPurpose ?? this.dataPurpose,
        consentRequired: consentRequired ?? this.consentRequired,
        crossBorderTransfer: crossBorderTransfer ?? this.crossBorderTransfer,
        backupEncryption: backupEncryption ?? this.backupEncryption,
        secureDeletion: secureDeletion ?? this.secureDeletion,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        securityPropertyStatus:
            securityPropertyStatus ?? this.securityPropertyStatus,
        changesDetected: changesDetected ?? this.changesDetected,
      );
}

class SystemPermissions {
  List<SystemPermissionsPostgre>? postgres;
  List<SystemPermissionsMongoDraft>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;

  SystemPermissions({
    this.postgres,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
  });

  SystemPermissions copyWith({
    List<SystemPermissionsPostgre>? postgres,
    List<SystemPermissionsMongoDraft>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
  }) =>
      SystemPermissions(
        postgres: postgres ?? this.postgres,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
      );
}

class SystemPermissionsMongoDraft {
  String? id;
  String? permissionId;
  String? permissionName;
  String? permissionType;
  String? resourceIdentifier;
  List<String>? actions;
  String? scope;
  String? entityId;
  String? attributeId;
  String? goId;
  String? loId;
  String? tenantId;
  String? description;
  String? naturalLanguage;
  int? version;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? permissionStatus;
  List<dynamic>? changesDetected;

  SystemPermissionsMongoDraft({
    this.id,
    this.permissionId,
    this.permissionName,
    this.permissionType,
    this.resourceIdentifier,
    this.actions,
    this.scope,
    this.entityId,
    this.attributeId,
    this.goId,
    this.loId,
    this.tenantId,
    this.description,
    this.naturalLanguage,
    this.version,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.permissionStatus,
    this.changesDetected,
  });

  SystemPermissionsMongoDraft copyWith({
    String? id,
    String? permissionId,
    String? permissionName,
    String? permissionType,
    String? resourceIdentifier,
    List<String>? actions,
    String? scope,
    String? entityId,
    String? attributeId,
    String? goId,
    String? loId,
    String? tenantId,
    String? description,
    String? naturalLanguage,
    int? version,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? permissionStatus,
    List<dynamic>? changesDetected,
  }) =>
      SystemPermissionsMongoDraft(
        id: id ?? this.id,
        permissionId: permissionId ?? this.permissionId,
        permissionName: permissionName ?? this.permissionName,
        permissionType: permissionType ?? this.permissionType,
        resourceIdentifier: resourceIdentifier ?? this.resourceIdentifier,
        actions: actions ?? this.actions,
        scope: scope ?? this.scope,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        goId: goId ?? this.goId,
        loId: loId ?? this.loId,
        tenantId: tenantId ?? this.tenantId,
        description: description ?? this.description,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        permissionStatus: permissionStatus ?? this.permissionStatus,
        changesDetected: changesDetected ?? this.changesDetected,
      );
}

class SystemPermissionsPostgre {
  String? permissionId;
  String? permissionName;
  String? permissionType;
  String? resourceIdentifier;
  List<String>? actions;
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? goId;
  String? loId;
  String? tenantId;

  SystemPermissionsPostgre({
    this.permissionId,
    this.permissionName,
    this.permissionType,
    this.resourceIdentifier,
    this.actions,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.goId,
    this.loId,
    this.tenantId,
  });

  SystemPermissionsPostgre copyWith({
    String? permissionId,
    String? permissionName,
    String? permissionType,
    String? resourceIdentifier,
    List<String>? actions,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? goId,
    String? loId,
    String? tenantId,
  }) =>
      SystemPermissionsPostgre(
        permissionId: permissionId ?? this.permissionId,
        permissionName: permissionName ?? this.permissionName,
        permissionType: permissionType ?? this.permissionType,
        resourceIdentifier: resourceIdentifier ?? this.resourceIdentifier,
        actions: actions ?? this.actions,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        goId: goId ?? this.goId,
        loId: loId ?? this.loId,
        tenantId: tenantId ?? this.tenantId,
      );
}

class TenantInfo {
  PostgresRecordElement? postgresRecord;
  PostgresRecordElement? mongoDraft;
  bool? foundInPostgres;
  bool? foundInMongo;

  TenantInfo({
    this.postgresRecord,
    this.mongoDraft,
    this.foundInPostgres,
    this.foundInMongo,
  });

  TenantInfo copyWith({
    PostgresRecordElement? postgresRecord,
    PostgresRecordElement? mongoDraft,
    bool? foundInPostgres,
    bool? foundInMongo,
  }) =>
      TenantInfo(
        postgresRecord: postgresRecord ?? this.postgresRecord,
        mongoDraft: mongoDraft ?? this.mongoDraft,
        foundInPostgres: foundInPostgres ?? this.foundInPostgres,
        foundInMongo: foundInMongo ?? this.foundInMongo,
      );
}

class Metadata {
  DateTime? retrievedAt;
  List<String>? dataSources;
  List<String>? categoriesIncluded;

  Metadata({
    this.retrievedAt,
    this.dataSources,
    this.categoriesIncluded,
  });

  Metadata copyWith({
    DateTime? retrievedAt,
    List<String>? dataSources,
    List<String>? categoriesIncluded,
  }) =>
      Metadata(
        retrievedAt: retrievedAt ?? this.retrievedAt,
        dataSources: dataSources ?? this.dataSources,
        categoriesIncluded: categoriesIncluded ?? this.categoriesIncluded,
      );
}

class Summary {
  int? totalItemsPostgres;
  int? totalItemsMongo;
  int? totalItemsCombined;
  Map<String, CategoryValue>? categories;
  bool? tenantExistsPostgres;
  bool? tenantExistsMongo;

  Summary({
    this.totalItemsPostgres,
    this.totalItemsMongo,
    this.totalItemsCombined,
    this.categories,
    this.tenantExistsPostgres,
    this.tenantExistsMongo,
  });

  Summary copyWith({
    int? totalItemsPostgres,
    int? totalItemsMongo,
    int? totalItemsCombined,
    Map<String, CategoryValue>? categories,
    bool? tenantExistsPostgres,
    bool? tenantExistsMongo,
  }) =>
      Summary(
        totalItemsPostgres: totalItemsPostgres ?? this.totalItemsPostgres,
        totalItemsMongo: totalItemsMongo ?? this.totalItemsMongo,
        totalItemsCombined: totalItemsCombined ?? this.totalItemsCombined,
        categories: categories ?? this.categories,
        tenantExistsPostgres: tenantExistsPostgres ?? this.tenantExistsPostgres,
        tenantExistsMongo: tenantExistsMongo ?? this.tenantExistsMongo,
      );
}

class CategoryValue {
  int? postgres;
  int? mongo;
  int? total;

  CategoryValue({
    this.postgres,
    this.mongo,
    this.total,
  });

  CategoryValue copyWith({
    int? postgres,
    int? mongo,
    int? total,
  }) =>
      CategoryValue(
        postgres: postgres ?? this.postgres,
        mongo: mongo ?? this.mongo,
        total: total ?? this.total,
      );
}
