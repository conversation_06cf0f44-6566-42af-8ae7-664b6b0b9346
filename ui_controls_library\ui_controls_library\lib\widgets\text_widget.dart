import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A highly configurable text input widget that supports various text formats and behaviors.
///
/// This widget provides a comprehensive text input field with customizable appearance,
/// validation, and behavior options. It can also display and format JSON data.
class TextWidget extends StatefulWidget {
  // Basic properties
  final String initialValue;
  final bool isRequired;
  final int? minLength;
  final int? maxLength;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final Color focusedBorderColor;
  final Color errorBorderColor;
  final double borderWidth;
  final double focusedBorderWidth;
  final double errorBorderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final String? fontFamily;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final Color? shadowColor;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final Color labelColor;
  final Color hintColor;
  final Color helperColor;
  final Color errorColor;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? helperStyle;
  final TextStyle? errorStyle;

  // Icon properties
  final bool showPrefix;
  final IconData? prefixIcon;
  final Color? prefixIconColor;
  final bool showSuffix;
  final IconData? suffixIcon;
  final Color? suffixIconColor;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final bool hasAnimation;
  final bool autoValidate;
  final bool showClearButton;
  final Color? clearButtonColor;
  final double clearButtonSize;
  final bool showCopyButton;
  final Color? copyButtonColor;
  final double copyButtonSize;
  final bool showPasteButton;
  final Color? pasteButtonColor;
  final double pasteButtonSize;
  final bool showCounterText;
  final bool enableInteractiveSelection;

  // Input properties
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final String obscuringCharacter;
  final bool showToggleVisibility;
  final bool autocorrect;
  final bool enableSuggestions;
  final TextCapitalization textCapitalization;

  // Validation properties
  final String? Function(String?)? validator;
  final RegExp? pattern;
  final String? patternErrorText;

  // Size properties
  final double? width;
  final double? height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Cursor properties
  final Color? cursorColor;
  final double cursorWidth;
  final double cursorHeight;
  final Radius? cursorRadius;

  // Callback properties
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onClear;
  final VoidCallback? onCopy;
  final VoidCallback? onPaste;
  final VoidCallback? onTap;

  // Advanced Interaction Properties
  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused (overrides focusedBorderColor)
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  // JSON Display Properties
  /// JSON data to display in the text field
  ///
  /// If provided, the text field will display this JSON data in a formatted way.
  /// This overrides the initialValue property.
  final dynamic jsonData;

  /// Whether to format the JSON data with indentation
  ///
  /// If true, the JSON data will be displayed with proper indentation.
  /// If false, the JSON data will be displayed as a compact string.
  final bool formatJson;

  /// The indentation level for formatted JSON
  ///
  /// The number of spaces to use for each level of indentation.
  /// Only used when formatJson is true.
  final int jsonIndent;

  /// Whether to allow editing of the JSON data
  ///
  /// If true, the user can edit the JSON data.
  /// If false, the JSON data is displayed as read-only.
  final bool editableJson;

  /// Whether to validate the JSON as the user types
  ///
  /// If true, the widget will validate the JSON as the user types.
  /// If false, no validation is performed.
  final bool validateJson;

  /// Whether to highlight JSON syntax with colors
  ///
  /// If true, different parts of the JSON (keys, values, etc.) will be displayed
  /// with different colors for better readability.
  final bool highlightJson;

  /// Color for JSON keys
  final Color jsonKeyColor;

  /// Color for JSON string values
  final Color jsonStringColor;

  /// Color for JSON number values
  final Color jsonNumberColor;

  /// Color for JSON boolean values
  final Color jsonBooleanColor;

  /// Color for JSON null values
  final Color jsonNullColor;

  const TextWidget({
    super.key,
    this.initialValue = '',
    this.isRequired = false,
    this.minLength,
    this.maxLength,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.focusedBorderColor = const Color(0xFF0058FF),
    this.errorBorderColor = Colors.red,
    this.borderWidth = 1.0,
    this.focusedBorderWidth = 1.0,
    this.errorBorderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.labelColor = Colors.black87,
    this.hintColor = Colors.black54,
    this.helperColor = Colors.black54,
    this.errorColor = Colors.red,
    this.labelStyle,
    this.hintStyle,
    this.helperStyle,
    this.errorStyle,
    this.showPrefix = false,
    this.prefixIcon,
    this.prefixIconColor,
    this.showSuffix = false,
    this.suffixIcon,
    this.suffixIconColor,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.hasAnimation = false,
    this.autoValidate = false,
    this.showClearButton = false,
    this.clearButtonColor,
    this.clearButtonSize = 20.0,
    this.showCopyButton = false,
    this.copyButtonColor,
    this.copyButtonSize = 20.0,
    this.showPasteButton = false,
    this.pasteButtonColor,
    this.pasteButtonSize = 20.0,
    this.showCounterText = false,
    this.enableInteractiveSelection = true,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.inputFormatters,
    this.obscureText = false,
    this.obscuringCharacter = '•',
    this.showToggleVisibility = false,
    this.autocorrect = true,
    this.enableSuggestions = true,
    this.textCapitalization = TextCapitalization.none,
    this.validator,
    this.pattern,
    this.patternErrorText,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
    this.margin = EdgeInsets.zero,
    this.cursorColor,
    this.cursorWidth = 2.0,
    this.cursorHeight = 16.0,
    this.cursorRadius,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.onCopy,
    this.onPaste,
    this.onTap,
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    this.jsonData,
    this.formatJson = true,
    this.jsonIndent = 2,
    this.editableJson = false,
    this.validateJson = true,
    this.highlightJson = true,
    this.jsonKeyColor = Colors.blue,
    this.jsonStringColor = Colors.green,
    this.jsonNumberColor = Colors.orange,
    this.jsonBooleanColor = Colors.purple,
    this.jsonNullColor = Colors.red,
  });

  /// Creates a TextWidget from a JSON map
  ///
  /// This factory constructor allows creating a TextWidget from a JSON map,
  /// making it easy to configure the widget from dynamic data.
  factory TextWidget.fromJson(Map<String, dynamic> json) {
    return TextWidget(
      key: json['key'] != null ? Key(json['key'].toString()) : null,
      initialValue: json['initialValue']?.toString() ?? '',
      isRequired: json['isRequired'] == true,
      minLength:
          json['minLength'] != null
              ? int.tryParse(json['minLength'].toString())
              : null,
      maxLength:
          json['maxLength'] != null
              ? int.tryParse(json['maxLength'].toString())
              : null,
      textColor:
          json['textColor'] != null
              ? Color(int.parse(json['textColor'].toString()))
              : Colors.black,
      backgroundColor:
          json['backgroundColor'] != null
              ? Color(int.parse(json['backgroundColor'].toString()))
              : Colors.white,
      borderColor:
          json['borderColor'] != null
              ? Color(int.parse(json['borderColor'].toString()))
              : const Color(0xFFCCCCCC),
      focusedBorderColor:
          json['focusedBorderColor'] != null
              ? Color(int.parse(json['focusedBorderColor'].toString()))
              : const Color(0xFF0058FF),
      errorBorderColor:
          json['errorBorderColor'] != null
              ? Color(int.parse(json['errorBorderColor'].toString()))
              : Colors.red,
      borderWidth:
          json['borderWidth'] != null
              ? double.tryParse(json['borderWidth'].toString()) ?? 1.0
              : 1.0,
      focusedBorderWidth:
          json['focusedBorderWidth'] != null
              ? double.tryParse(json['focusedBorderWidth'].toString()) ?? 1.0
              : 1.0,
      errorBorderWidth:
          json['errorBorderWidth'] != null
              ? double.tryParse(json['errorBorderWidth'].toString()) ?? 1.0
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? double.tryParse(json['borderRadius'].toString()) ?? 4.0
              : 4.0,
      hasBorder: json['hasBorder'] == true,
      fontSize:
          json['fontSize'] != null
              ? double.tryParse(json['fontSize'].toString()) ?? 16.0
              : 16.0,
      fontWeight:
          json['fontWeight'] != null
              ? FontWeight.values[int.tryParse(json['fontWeight'].toString()) ??
                  3]
              : FontWeight.normal,
      fontFamily: json['fontFamily']?.toString(),
      isCompact: json['isCompact'] == true,
      hasShadow: json['hasShadow'] == true,
      elevation:
          json['elevation'] != null
              ? double.tryParse(json['elevation'].toString()) ?? 2.0
              : 2.0,
      shadowColor:
          json['shadowColor'] != null
              ? Color(int.parse(json['shadowColor'].toString()))
              : null,
      isDarkTheme: json['isDarkTheme'] == true,
      textAlign:
          json['textAlign'] != null
              ? TextAlign.values[int.tryParse(json['textAlign'].toString()) ??
                  0]
              : TextAlign.start,
      label: json['label']?.toString(),
      hint: json['hint']?.toString(),
      helperText: json['helperText']?.toString(),
      errorText: json['errorText']?.toString(),
      labelColor:
          json['labelColor'] != null
              ? Color(int.parse(json['labelColor'].toString()))
              : Colors.black87,
      hintColor:
          json['hintColor'] != null
              ? Color(int.parse(json['hintColor'].toString()))
              : Colors.black54,
      helperColor:
          json['helperColor'] != null
              ? Color(int.parse(json['helperColor'].toString()))
              : Colors.black54,
      errorColor:
          json['errorColor'] != null
              ? Color(int.parse(json['errorColor'].toString()))
              : Colors.red,
      showPrefix: json['showPrefix'] == true,
      //prefixIcon: json['prefixIcon'] != null ? IconData(int.parse(json['prefixIcon'].toString()), fontFamily: 'MaterialIcons') : null,
      prefixIconColor:
          json['prefixIconColor'] != null
              ? Color(int.parse(json['prefixIconColor'].toString()))
              : null,
      showSuffix: json['showSuffix'] == true,
      // suffixIcon: json['suffixIcon'] != null ? IconData(int.parse(json['suffixIcon'].toString()), fontFamily: 'MaterialIcons') : null,
      suffixIconColor:
          json['suffixIconColor'] != null
              ? Color(int.parse(json['suffixIconColor'].toString()))
              : null,
      isReadOnly: json['isReadOnly'] == true,
      isDisabled: json['isDisabled'] == true,
      autofocus: json['autofocus'] == true,
      hasAnimation: json['hasAnimation'] == true,
      autoValidate: json['autoValidate'] == true,
      showClearButton: json['showClearButton'] == true,
      clearButtonColor:
          json['clearButtonColor'] != null
              ? Color(int.parse(json['clearButtonColor'].toString()))
              : null,
      clearButtonSize:
          json['clearButtonSize'] != null
              ? double.tryParse(json['clearButtonSize'].toString()) ?? 20.0
              : 20.0,
      showCopyButton: json['showCopyButton'] == true,
      copyButtonColor:
          json['copyButtonColor'] != null
              ? Color(int.parse(json['copyButtonColor'].toString()))
              : null,
      copyButtonSize:
          json['copyButtonSize'] != null
              ? double.tryParse(json['copyButtonSize'].toString()) ?? 20.0
              : 20.0,
      showPasteButton: json['showPasteButton'] == true,
      pasteButtonColor:
          json['pasteButtonColor'] != null
              ? Color(int.parse(json['pasteButtonColor'].toString()))
              : null,
      pasteButtonSize:
          json['pasteButtonSize'] != null
              ? double.tryParse(json['pasteButtonSize'].toString()) ?? 20.0
              : 20.0,
      showCounterText: json['showCounterText'] == true,
      enableInteractiveSelection: json['enableInteractiveSelection'] != false,
      keyboardType:
          json['keyboardType'] != null
              ? TextInputType
                  .values[int.tryParse(json['keyboardType'].toString()) ?? 0]
              : TextInputType.text,
      textInputAction:
          json['textInputAction'] != null
              ? TextInputAction
                  .values[int.tryParse(json['textInputAction'].toString()) ?? 0]
              : TextInputAction.done,
      obscureText: json['obscureText'] == true,
      obscuringCharacter: json['obscuringCharacter']?.toString() ?? '•',
      showToggleVisibility: json['showToggleVisibility'] == true,
      autocorrect: json['autocorrect'] != false,
      enableSuggestions: json['enableSuggestions'] != false,
      textCapitalization:
          json['textCapitalization'] != null
              ? TextCapitalization.values[int.tryParse(
                    json['textCapitalization'].toString(),
                  ) ??
                  0]
              : TextCapitalization.none,
      width:
          json['width'] != null
              ? double.tryParse(json['width'].toString())
              : null,
      height:
          json['height'] != null
              ? double.tryParse(json['height'].toString())
              : null,
      padding:
          json['padding'] != null
              ? EdgeInsets.fromLTRB(
                double.tryParse(
                      json['padding']['left']?.toString() ?? '12.0',
                    ) ??
                    12.0,
                double.tryParse(json['padding']['top']?.toString() ?? '12.0') ??
                    12.0,
                double.tryParse(
                      json['padding']['right']?.toString() ?? '12.0',
                    ) ??
                    12.0,
                double.tryParse(
                      json['padding']['bottom']?.toString() ?? '12.0',
                    ) ??
                    12.0,
              )
              : const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      margin:
          json['margin'] != null
              ? EdgeInsets.fromLTRB(
                double.tryParse(json['margin']['left']?.toString() ?? '0.0') ??
                    0.0,
                double.tryParse(json['margin']['top']?.toString() ?? '0.0') ??
                    0.0,
                double.tryParse(json['margin']['right']?.toString() ?? '0.0') ??
                    0.0,
                double.tryParse(
                      json['margin']['bottom']?.toString() ?? '0.0',
                    ) ??
                    0.0,
              )
              : EdgeInsets.zero,
      cursorColor:
          json['cursorColor'] != null
              ? Color(int.parse(json['cursorColor'].toString()))
              : null,
      cursorWidth:
          json['cursorWidth'] != null
              ? double.tryParse(json['cursorWidth'].toString()) ?? 2.0
              : 2.0,
      cursorHeight:
          json['cursorHeight'] != null
              ? double.tryParse(json['cursorHeight'].toString()) ?? 16.0
              : 16.0,
      cursorRadius:
          json['cursorRadius'] != null
              ? Radius.circular(
                double.tryParse(json['cursorRadius'].toString()) ?? 1.0,
              )
              : null,
      // Advanced interaction properties
      onHover: null, // Callbacks can't be serialized
      onFocus: null, // Callbacks can't be serialized
      hoverColor:
          json['hoverColor'] != null
              ? Color(int.parse(json['hoverColor'].toString()))
              : null,
      focusColor:
          json['focusColor'] != null
              ? Color(int.parse(json['focusColor'].toString()))
              : null,
      tooltip: json['tooltip']?.toString(),
      semanticsLabel: json['semanticsLabel']?.toString(),
      enableFeedback: json['enableFeedback'] != false,
      onDoubleTap: null, // Callbacks can't be serialized
      onLongPress: null, // Callbacks can't be serialized
      // JSON display properties
      jsonData: json['jsonData'],
      formatJson: json['formatJson'] != false,
      jsonIndent:
          json['jsonIndent'] != null
              ? int.tryParse(json['jsonIndent'].toString()) ?? 2
              : 2,
      editableJson: json['editableJson'] == true,
      validateJson: json['validateJson'] != false,
      highlightJson: json['highlightJson'] != false,
      jsonKeyColor:
          json['jsonKeyColor'] != null
              ? Color(int.parse(json['jsonKeyColor'].toString()))
              : Colors.blue,
      jsonStringColor:
          json['jsonStringColor'] != null
              ? Color(int.parse(json['jsonStringColor'].toString()))
              : Colors.green,
      jsonNumberColor:
          json['jsonNumberColor'] != null
              ? Color(int.parse(json['jsonNumberColor'].toString()))
              : Colors.orange,
      jsonBooleanColor:
          json['jsonBooleanColor'] != null
              ? Color(int.parse(json['jsonBooleanColor'].toString()))
              : Colors.purple,
      jsonNullColor:
          json['jsonNullColor'] != null
              ? Color(int.parse(json['jsonNullColor'].toString()))
              : Colors.red,
    );
  }

  @override
  TextWidgetState createState() => TextWidgetState();
}

class TextWidgetState extends State<TextWidget>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  String? _errorText;
  bool _isObscured = true;
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isHovered = false;
  bool _hasFocus = false;

  // JSON parsing state
  String? _jsonError;

  @override
  void initState() {
    super.initState();

    // Initialize controller with initial value or formatted JSON data
    String initialText = widget.initialValue;

    // If JSON data is provided, format it
    if (widget.jsonData != null) {
      initialText = _formatJsonData(widget.jsonData);
    }

    _controller = TextEditingController(text: initialText);
    _focusNode = FocusNode();
    _isObscured = widget.obscureText;

    // Set up animation
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Initial validation
    if (widget.autoValidate && initialText.isNotEmpty) {
      _validate(initialText);
    }

    // Initial JSON validation if needed
    if (widget.jsonData != null && widget.validateJson) {
      _validateJsonData(initialText);
    }

    // Listen for focus changes
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });

    if (_hasFocus) {
      // Handle focus gained
      if (widget.onFocus != null) {
        widget.onFocus!(true);
      }
    } else {
      // Handle focus lost
      if (widget.autoValidate) {
        _validate(_controller.text);
      }

      if (widget.onFocus != null) {
        widget.onFocus!(false);
      }
    }
  }

  bool _validate(String value) {
    setState(() {
      // Check if required
      if (widget.isRequired && value.isEmpty) {
        _errorText = 'This field is required';
        return;
      }

      // Check min length
      if (widget.minLength != null &&
          value.isNotEmpty &&
          value.length < widget.minLength!) {
        _errorText = 'Minimum length is ${widget.minLength} characters';
        return;
      }

      // Check pattern
      if (widget.pattern != null &&
          value.isNotEmpty &&
          !widget.pattern!.hasMatch(value)) {
        _errorText = widget.patternErrorText ?? 'Invalid format';
        return;
      }

      // Check custom validator
      if (widget.validator != null) {
        _errorText = widget.validator!(value);
        return;
      }

      // No errors
      _errorText = null;
    });

    return _errorText == null;
  }

  /// Formats JSON data based on widget settings
  String _formatJsonData(dynamic data) {
    try {
      // Convert data to a JSON-compatible format
      dynamic jsonData;

      if (data is String) {
        try {
          // Try to parse as JSON
          jsonData = jsonDecode(data);
        } catch (e) {
          // If not valid JSON, return as is
          return data;
        }
      } else {
        jsonData = data;
      }

      // Format the JSON data
      if (widget.formatJson) {
        final indent = ' ' * widget.jsonIndent;
        return JsonEncoder.withIndent(indent).convert(jsonData);
      } else {
        return jsonEncode(jsonData);
      }
    } catch (e) {
      // If formatting fails, return string representation
      return data.toString();
    }
  }

  /// Validates JSON data
  bool _validateJsonData(String value) {
    try {
      jsonDecode(value);
      _jsonError = null;
      return true;
    } catch (e) {
      _jsonError = e.toString();
      return false;
    }
  }

  void _handleValueChanged(String value) {
    if (widget.autoValidate) {
      _validate(value);
    }

    // Validate JSON if needed
    if (widget.jsonData != null && widget.validateJson && widget.editableJson) {
      _validateJsonData(value);
    }

    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }
  }

  void _handleClear() {
    setState(() {
      _controller.clear();
      _errorText = null;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onChanged != null) {
      widget.onChanged!('');
    }
  }

  Future<void> _handlePaste() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      setState(() {
        _controller.text = data.text!;
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: data.text!.length),
        );
      });

      if (widget.autoValidate) {
        _validate(_controller.text);
      }

      if (widget.onPaste != null) {
        widget.onPaste!();
      }

      if (widget.onChanged != null) {
        widget.onChanged!(_controller.text);
      }
    }
  }

  void _handleCopy() {
    Clipboard.setData(ClipboardData(text: _controller.text));

    if (widget.onCopy != null) {
      widget.onCopy!();
    }
  }

  void _toggleVisibility() {
    setState(() {
      _isObscured = !_isObscured;
    });
  }

  void _handleHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 56.0;
    } else if (screenWidth >= 1440) {
      return 48.0;
    } else if (screenWidth >= 1280) {
      return 40.0;
    } else {
      return 40.0;
    }
  }

  /// Builds a RichText widget with JSON syntax highlighting
  Widget _buildJsonSyntaxHighlightedText(String jsonText) {
    try {
      // Try to parse the JSON to validate it
      final dynamic parsedJson = jsonDecode(jsonText);

      // Convert back to a formatted string
      final formattedJson =
          widget.formatJson
              ? JsonEncoder.withIndent(
                ' ' * widget.jsonIndent,
              ).convert(parsedJson)
              : jsonEncode(parsedJson);

      // Create spans with syntax highlighting
      final List<TextSpan> spans = [];

      // Simple syntax highlighting for demonstration
      // A more sophisticated approach would use a proper JSON parser
      final lines = formattedJson.split('\n');

      for (final line in lines) {
        // Process each line
        String remainingLine = line;
        List<TextSpan> lineSpans = [];

        // Handle indentation
        final indentMatch = RegExp(r'^(\s+)').firstMatch(remainingLine);
        if (indentMatch != null) {
          final indent = indentMatch.group(1)!;
          lineSpans.add(
            TextSpan(
              text: indent,
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );
          remainingLine = remainingLine.substring(indent.length);
        }

        // Handle key-value pairs
        final keyValueMatch = RegExp(
          r'^"([^"]+)"\s*:',
        ).firstMatch(remainingLine);
        if (keyValueMatch != null) {
          final key = keyValueMatch.group(1)!;
          final fullMatch = keyValueMatch.group(0)!;

          // Add the key with its color
          lineSpans.add(
            TextSpan(
              text: '"$key"',
              style: TextStyle(
                color: widget.jsonKeyColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );

          // Add the colon
          lineSpans.add(
            TextSpan(
              text: ':',
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );

          remainingLine = remainingLine.substring(fullMatch.length);
        }

        // Handle string values
        final stringMatch = RegExp(r'^\s*"([^"]*)"').firstMatch(remainingLine);
        if (stringMatch != null) {
          // We don't need the value itself, just the full match for display
          final fullMatch = stringMatch.group(0)!;

          // Add the string value with its color
          lineSpans.add(
            TextSpan(
              text: fullMatch,
              style: TextStyle(
                color: widget.jsonStringColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );

          remainingLine = remainingLine.substring(fullMatch.length);
        }

        // Handle numeric values
        final numberMatch = RegExp(
          r'^\s*(-?\d+(\.\d+)?)',
        ).firstMatch(remainingLine);
        if (numberMatch != null) {
          // We don't need the value itself, just the full match for display
          final fullMatch = numberMatch.group(0)!;

          // Add the number value with its color
          lineSpans.add(
            TextSpan(
              text: fullMatch,
              style: TextStyle(
                color: widget.jsonNumberColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );

          remainingLine = remainingLine.substring(fullMatch.length);
        }

        // Handle boolean values
        final boolMatch = RegExp(r'^\s*(true|false)').firstMatch(remainingLine);
        if (boolMatch != null) {
          // We don't need the value itself, just the full match for display
          final fullMatch = boolMatch.group(0)!;

          // Add the boolean value with its color
          lineSpans.add(
            TextSpan(
              text: fullMatch,
              style: TextStyle(
                color: widget.jsonBooleanColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );

          remainingLine = remainingLine.substring(fullMatch.length);
        }

        // Handle null values
        final nullMatch = RegExp(r'^\s*(null)').firstMatch(remainingLine);
        if (nullMatch != null) {
          final fullMatch = nullMatch.group(0)!;

          // Add the null value with its color
          lineSpans.add(
            TextSpan(
              text: fullMatch,
              style: TextStyle(
                color: widget.jsonNullColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );

          remainingLine = remainingLine.substring(fullMatch.length);
        }

        // Add any remaining text
        if (remainingLine.isNotEmpty) {
          lineSpans.add(
            TextSpan(
              text: remainingLine,
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          );
        }

        // Add the line spans to the main spans list
        spans.addAll(lineSpans);

        // Add a newline except for the last line
        if (line != lines.last) {
          spans.add(const TextSpan(text: '\n'));
        }
      }

      return SelectableText.rich(
        TextSpan(children: spans),
        textAlign: widget.textAlign,
      );
    } catch (e) {
      // If parsing fails, display the raw text
      return SelectableText(
        jsonText,
        style: TextStyle(
          color: widget.textColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        ),
        textAlign: widget.textAlign,
      );
    }
  }

  /// Builds a text field with JSON syntax highlighting
  Widget _buildJsonHighlightedTextField(
    Color textColor,
    Color backgroundColor,
    Color borderColor,
    double borderWidth,
    TextStyle labelStyle,
    TextStyle hintStyle,
    TextStyle helperStyle,
    TextStyle errorStyle,
    Color prefixIconColor,
    List<Widget> suffixWidgets,
    Color cursorColor,
  ) {
    // If JSON data is not editable, use a read-only field with syntax highlighting
    if (!widget.editableJson) {
      return Container(
        width: widget.width,
        height: widget.height,
        padding: widget.padding,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border:
              widget.hasBorder
                  ? Border.all(color: borderColor, width: borderWidth)
                  : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.label != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Text(widget.label!, style: labelStyle),
              ),
            Expanded(
              child: SingleChildScrollView(
                child: _buildJsonSyntaxHighlightedText(_controller.text),
              ),
            ),
            if (_jsonError != null && widget.validateJson)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(_jsonError!, style: errorStyle),
              ),
            if (widget.helperText != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(widget.helperText!, style: helperStyle),
              ),
          ],
        ),
      );
    }

    // If JSON data is editable, use a standard text field but with error validation
    return Theme(
      data: Theme.of(context).copyWith(
        inputDecorationTheme: const InputDecorationTheme(
          hoverColor: Colors.transparent,
          focusColor: Colors.transparent,
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        style: TextStyle(
          color: textColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        ),
        textAlign: widget.textAlign,
        readOnly: !widget.editableJson || widget.isReadOnly,
        enabled: !widget.isDisabled,
        maxLines: null, // Allow multiple lines for JSON
        decoration: InputDecoration(
          labelText: widget.label,
          hintText: widget.hint,
          helperText: widget.helperText,
          errorText: _jsonError,
          labelStyle: labelStyle,
          hintStyle: hintStyle,
          helperStyle: helperStyle,
          errorStyle: errorStyle,
          filled: true,
          fillColor: backgroundColor,
          prefixIcon:
              widget.showPrefix && widget.prefixIcon != null
                  ? Icon(widget.prefixIcon, color: prefixIconColor)
                  : null,
          suffixIcon:
              suffixWidgets.isEmpty
                  ? null
                  : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: suffixWidgets,
                  ),
          contentPadding: widget.padding,
          border:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: borderColor,
                      width: borderWidth,
                    ),
                  )
                  : InputBorder.none,
          enabledBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: borderColor,
                      width: borderWidth,
                    ),
                  )
                  : null,
          focusedBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: widget.focusedBorderColor,
                      width: widget.focusedBorderWidth,
                    ),
                  )
                  : null,
          errorBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: widget.errorBorderColor,
                      width: widget.errorBorderWidth,
                    ),
                  )
                  : null,
          focusedErrorBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: widget.errorBorderColor,
                      width: widget.errorBorderWidth,
                    ),
                  )
                  : null,
          counterText: widget.showCounterText ? null : '',
        ),
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.newline,
        cursorColor: cursorColor,
        cursorWidth: widget.cursorWidth,
        cursorHeight: widget.cursorHeight,
        cursorRadius: widget.cursorRadius,
        onChanged: (value) {
          setState(() {
            if (widget.validateJson) {
              _validateJsonData(value);
            }
          });

          if (widget.onChanged != null) {
            widget.onChanged!(value);
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Determine effective colors based on theme and disabled state
    final effectiveTextColor =
        widget.isDisabled
            ? Colors.grey
            : widget.isDarkTheme
            ? Colors.white
            : widget.textColor;

    final effectiveBorderColor =
        _errorText != null
            ? widget.errorBorderColor
            : _focusNode.hasFocus
            ? widget.focusedBorderColor
            : _isHovered
            ? widget.focusedBorderColor
            : widget.borderColor;

    final effectiveBorderWidth =
        _errorText != null
            ? widget.errorBorderWidth
            : _focusNode.hasFocus || _isHovered
            ? 1.0
            : widget.borderWidth;

    final effectiveBackgroundColor =
        widget.isDisabled
            ? Colors.grey.shade200
            : _hasFocus && widget.focusColor != null
            ? widget.focusColor!
            : _isHovered && widget.hoverColor != null
            ? widget.hoverColor!
            : widget.isDarkTheme
            ? Colors.grey.shade800
            : widget.backgroundColor;

    final effectiveClearButtonColor =
        widget.clearButtonColor ??
        (widget.isDarkTheme ? Colors.white70 : Colors.black54);

    final effectiveCopyButtonColor =
        widget.copyButtonColor ??
        (widget.isDarkTheme ? Colors.white70 : Colors.black54);

    final effectivePasteButtonColor =
        widget.pasteButtonColor ??
        (widget.isDarkTheme ? Colors.white70 : Colors.black54);

    final effectivePrefixIconColor =
        widget.prefixIconColor ??
        (widget.isDarkTheme ? Colors.white70 : Colors.black54);

    final effectiveSuffixIconColor =
        widget.suffixIconColor ??
        (widget.isDarkTheme ? Colors.white70 : Colors.black54);

    final effectiveCursorColor =
        widget.cursorColor ??
        (widget.isDarkTheme ? Colors.white : theme.primaryColor);

    final effectiveShadowColor =
        widget.shadowColor ??
        (widget.isDarkTheme ? Colors.black : Colors.black26);

    // Create text styles
    // final textStyle = TextStyle(
    //   color: effectiveTextColor,
    //   fontSize: widget.fontSize,
    //   fontWeight: widget.fontWeight,
    //   fontFamily: widget.fontFamily,
    // );

    final textStyle = FontManager.getCustomStyle(
      fontFamily: FontManager.fontFamilyInter,
      fontWeight: FontManager.medium,
      fontSize: _getResponsiveValueFontSize(context),
      color: effectiveTextColor,
    );
    final labelStyle =
        widget.labelStyle ??
        // TextStyle(
        //   color: widget.isDisabled ? Colors.grey : widget.labelColor,
        //   fontSize: widget.fontSize * 0.9,
        // );
        FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          fontSize: _getResponsiveValueFontSize(context),
          color: widget.isDisabled ? Colors.grey : widget.labelColor,
        );

    final hintStyle =
        widget.hintStyle ??
        // TextStyle(
        //   color: widget.isDisabled ? Colors.grey : widget.hintColor,
        //   fontSize: widget.fontSize,
        // );
        FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          color: widget.isDisabled ? Colors.grey : widget.hintColor,
          fontSize: _getResponsiveValueFontSize(context),
        );

    final helperStyle =
        widget.helperStyle ??
        // TextStyle(
        //   color: widget.isDisabled ? Colors.grey : widget.helperColor,
        //   fontSize: widget.fontSize * 0.8,
        // );
        FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          fontSize: _getResponsiveValueFontSize(context),
          color: widget.isDisabled ? Colors.grey : widget.helperColor,
        );

    final errorStyle =
        widget.errorStyle ??
        //TextStyle(color: widget.errorColor, fontSize: widget.fontSize * 0.8);
        FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          fontSize: _getResponsiveValueFontSize(context),
          color: widget.errorColor,
        );

    // Create suffix widgets
    final List<Widget> suffixWidgets = [];

    if (widget.showToggleVisibility && widget.obscureText) {
      suffixWidgets.add(
        IconButton(
          icon: Icon(
            _isObscured ? Icons.visibility_off : Icons.visibility,
            color: widget.isDisabled ? Colors.grey : effectiveSuffixIconColor,
            size: 20.0,
          ),
          onPressed: widget.isDisabled ? null : _toggleVisibility,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        ),
      );
    }

    if (widget.showClearButton && _controller.text.isNotEmpty) {
      suffixWidgets.add(
        IconButton(
          icon: Icon(
            Icons.clear,
            color: widget.isDisabled ? Colors.grey : effectiveClearButtonColor,
            size: widget.clearButtonSize,
          ),
          onPressed:
              widget.isDisabled || widget.isReadOnly ? null : _handleClear,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        ),
      );
    }

    if (widget.showCopyButton && _controller.text.isNotEmpty) {
      suffixWidgets.add(
        IconButton(
          icon: Icon(
            Icons.copy,
            color: widget.isDisabled ? Colors.grey : effectiveCopyButtonColor,
            size: widget.copyButtonSize,
          ),
          onPressed: widget.isDisabled ? null : _handleCopy,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        ),
      );
    }

    if (widget.showPasteButton) {
      suffixWidgets.add(
        IconButton(
          icon: Icon(
            Icons.paste,
            color: widget.isDisabled ? Colors.grey : effectivePasteButtonColor,
            size: widget.pasteButtonSize,
          ),
          onPressed:
              widget.isDisabled || widget.isReadOnly ? null : _handlePaste,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        ),
      );
    }

    if (widget.showSuffix && widget.suffixIcon != null) {
      suffixWidgets.add(
        Icon(
          widget.suffixIcon,
          color: widget.isDisabled ? Colors.grey : effectiveSuffixIconColor,
        ),
      );
    }

    // Create the text field with all the specified properties
    late Widget textField;

    // Check if we need to display JSON data
    if (widget.jsonData != null) {
      if (widget.highlightJson) {
        // Use the JSON syntax highlighting widget
        textField = Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                widget.hasBorder
                    ? Border.all(
                      color:
                          _hasFocus
                              ? widget.focusedBorderColor
                              : effectiveBorderColor,
                      width:
                          _hasFocus
                              ? widget.focusedBorderWidth
                              : effectiveBorderWidth,
                    )
                    : null,
            boxShadow:
                widget.hasShadow
                    ? [
                      BoxShadow(
                        color: effectiveShadowColor,
                        blurRadius: widget.elevation,
                        offset: Offset(0, widget.elevation / 2),
                      ),
                    ]
                    : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.label != null)
                Padding(
                  padding: const EdgeInsets.only(
                    left: 12.0,
                    top: 8.0,
                    bottom: 4.0,
                  ),
                  child: Text(widget.label!, style: labelStyle),
                ),
              Expanded(
                child: SingleChildScrollView(
                  padding: widget.padding,
                  child: _buildJsonSyntaxHighlightedText(_controller.text),
                ),
              ),
              if (_jsonError != null && widget.validateJson)
                Padding(
                  padding: const EdgeInsets.only(left: 12.0, bottom: 8.0),
                  child: Text(_jsonError!, style: errorStyle),
                ),
              if (widget.helperText != null)
                Padding(
                  padding: const EdgeInsets.only(left: 12.0, bottom: 8.0),
                  child: Text(widget.helperText!, style: helperStyle),
                ),
            ],
          ),
        );
      } else {
        // Use a standard text field but with JSON data
        textField = Theme(
          data: Theme.of(context).copyWith(
            inputDecorationTheme: const InputDecorationTheme(
              hoverColor: Colors.transparent,
              focusColor: Colors.transparent,
            ),
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            style: textStyle,
            textAlign: widget.textAlign,
            readOnly: !widget.editableJson || widget.isReadOnly,
            enabled: !widget.isDisabled,
            maxLines: null, // Allow multiple lines for JSON
            decoration: InputDecoration(
              labelText: widget.label,
              hintText: widget.hint,
              helperText: widget.helperText,
              errorText: _jsonError,
              labelStyle: labelStyle,
              hintStyle: hintStyle,
              helperStyle: helperStyle,
              errorStyle: errorStyle,
              filled: true,
              fillColor: effectiveBackgroundColor,
              focusColor: Colors.red,
              prefixIcon:
                  widget.showPrefix && widget.prefixIcon != null
                      ? Icon(widget.prefixIcon, color: effectivePrefixIconColor)
                      : null,
              suffixIcon:
                  suffixWidgets.isEmpty
                      ? null
                      : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: suffixWidgets,
                      ),
              contentPadding: widget.padding,
              border:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: effectiveBorderColor,
                          width: widget.borderWidth,
                        ),
                      )
                      : InputBorder.none,
              enabledBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: effectiveBorderColor,
                          width: widget.borderWidth,
                        ),
                      )
                      : null,
              focusedBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: effectiveBorderColor,
                          //  width: effectiveBorderColor,
                        ),
                      )
                      : null,
              errorBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: widget.errorBorderColor,
                          width: widget.errorBorderWidth,
                        ),
                      )
                      : null,
              focusedErrorBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: widget.errorBorderColor,
                          width: widget.errorBorderWidth,
                        ),
                      )
                      : null,
              counterText: widget.showCounterText ? null : '',
            ),
            cursorColor: effectiveCursorColor,
            cursorWidth: widget.cursorWidth,
            cursorHeight: widget.cursorHeight,
            cursorRadius: widget.cursorRadius,
            onChanged: (value) {
              if (widget.validateJson) {
                setState(() {
                  _validateJsonData(value);
                });
              }

              if (widget.onChanged != null) {
                widget.onChanged!(value);
              }
            },
          ),
        );
      }
    } else {
      // Regular text field (not JSON)
      textField = Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            hoverColor: Colors.transparent,
            focusColor: Colors.transparent,
          ),
        ),
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          // style: TextStyle(
          //   fontWeight: FontWeight.w500,
          //   fontFamily: 'Inter',
          //   fontSize: _getResponsiveValueFontSize(context),
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            fontSize: _getResponsiveValueFontSize(context),
          ),
          textAlign: widget.textAlign,
          obscureText: widget.obscureText && _isObscured,
          obscuringCharacter: widget.obscuringCharacter,
          decoration: InputDecoration(
            //hintText: widget.hint,
            hintText: 'Type Here',
            helperText: widget.helperText,
            errorText: _errorText ?? widget.errorText,
            labelStyle: labelStyle,
            hintStyle: hintStyle,
            helperStyle: helperStyle,
            errorStyle: errorStyle,
            filled: true,
            focusColor: Colors.white,
            fillColor:
                widget.isDisabled ? Colors.white : effectiveBackgroundColor,
            prefixIcon:
                widget.showPrefix && widget.prefixIcon != null
                    ? Icon(
                      widget.prefixIcon,
                      color:
                          widget.isDisabled
                              ? Colors.grey
                              : effectivePrefixIconColor,
                    )
                    : null,
            suffixIcon:
                suffixWidgets.isEmpty
                    ? null
                    : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: suffixWidgets,
                    ),
            contentPadding:
                widget.isCompact
                    ? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0)
                    : widget.padding,
            border:
                widget.hasBorder
                    ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      borderSide: BorderSide(
                        color: effectiveBorderColor,
                        width: effectiveBorderWidth,
                      ),
                    )
                    : InputBorder.none,
            enabledBorder:
                widget.hasBorder
                    ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      borderSide: BorderSide(
                        color: effectiveBorderColor,
                        width: effectiveBorderWidth,
                      ),
                    )
                    : null,
            focusedBorder:
                widget.hasBorder
                    ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      borderSide: BorderSide(
                        color: effectiveBorderColor,
                        width: widget.focusedBorderWidth,
                      ),
                    )
                    : null,
            errorBorder:
                widget.hasBorder
                    ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      borderSide: BorderSide(
                        color: widget.errorBorderColor,
                        width: widget.errorBorderWidth,
                      ),
                    )
                    : null,
            focusedErrorBorder:
                widget.hasBorder
                    ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      borderSide: BorderSide(
                        color: widget.errorBorderColor,
                        width: widget.errorBorderWidth,
                      ),
                    )
                    : null,
            counterText: widget.showCounterText ? null : '',
          ),
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          textCapitalization: widget.textCapitalization,
          inputFormatters: widget.inputFormatters,
          enabled: !widget.isDisabled && !widget.isReadOnly,
          readOnly: widget.isReadOnly,
          autofocus: widget.autofocus,
          autocorrect: widget.autocorrect,
          enableSuggestions: widget.enableSuggestions,
          enableInteractiveSelection: widget.enableInteractiveSelection,
          maxLength: widget.maxLength,
          cursorColor: effectiveCursorColor,
          cursorWidth: widget.cursorWidth,
          cursorHeight: widget.cursorHeight,
          cursorRadius: widget.cursorRadius,
          onChanged: _handleValueChanged,
          onSubmitted: (value) {
            final isValid = _validate(value);

            if (widget.onSubmitted != null && isValid) {
              widget.onSubmitted!(value);
            }
          },
          onTap: () {
            if (widget.onTap != null) {
              widget.onTap!();
            }

            if (widget.isCompact) {
              // Select all text when tapped in compact mode
              _controller.selection = TextSelection(
                baseOffset: 0,
                extentOffset: _controller.text.length,
              );
            }
          },
        ),
      );
    }

    // Apply shadow if needed
    // if (widget.hasShadow) {
    //   textField = Container(
    //     decoration: BoxDecoration(
    //       borderRadius: BorderRadius.circular(widget.borderRadius),
    //       boxShadow: [
    //         BoxShadow(
    //           color: effectiveShadowColor,
    //           blurRadius: widget.elevation * 2,
    //           spreadRadius: widget.elevation / 2,
    //           offset: const Offset(0, 1),
    //         ),
    //       ],
    //     ),
    //     child: textField,
    //   );
    // }

    // Apply animation if needed
    // if (widget.hasAnimation) {
    //   textField = FadeTransition(
    //     opacity: _animation,
    //     child: textField,
    //   );
    // }

    // Apply size constraints with responsive height
    textField = SizedBox(
      width: widget.width,
      height: widget.height ?? _getResponsiveHeight(context),
      child: textField,
    );

    // Apply margin if needed
    if (widget.margin != EdgeInsets.zero) {
      textField = Padding(padding: widget.margin, child: textField);
    }

    // Add hover detection - always enabled for consistent behavior
    textField = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: textField,
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      textField = Tooltip(message: widget.tooltip!, child: textField);
    }

    // Add gesture detection for advanced interactions
    if (widget.onDoubleTap != null || widget.onLongPress != null) {
      textField = GestureDetector(
        onDoubleTap: widget.onDoubleTap,
        onLongPress: widget.onLongPress,
        child: textField,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      textField = Semantics(label: widget.semanticsLabel, child: textField);
    }

    // Wrap in Column with label above if label is provided
    if (widget.label != null && widget.label!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Text(
              widget.label!,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontFamily: 'Inter',
                fontSize: _getResponsiveFontSize(context),
              ),
            ),
          ),
          textField,
        ],
      );
    }

    return textField;
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 15.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 12.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 12.0; // Small (768-1024px) - Increased for readability
  } else {
    return 12.0; // Default for very small screens - Consistent
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
