import 'dart:developer';
import 'dart:math' as math;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:nsl/models/RoleCreatModel.dart';
import 'package:nsl/models/add_role_model.dart';
import 'package:nsl/models/roles/inheritance_role_model.dart';
import 'package:nsl/models/roles/validate_department_model.dart';
import 'package:nsl/models/roles_expansion_model.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:provider/provider.dart';

class RoleExpensionPanelDetailsStatic extends StatefulWidget {
  final String? sessionId;
  final String? userIntent;

  const RoleExpensionPanelDetailsStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<RoleExpensionPanelDetailsStatic> createState() =>
      _RoleExpensionPanelDetailsStaticState();
}

class _RoleExpensionPanelDetailsStaticState
    extends State<RoleExpensionPanelDetailsStatic> {
     final Map<String, dynamic> data = {
    "roles": [
      {
        "roleName": "CEO",
        "role_configuration": {
          "roleName": "CEO",
          "description": "Chief Executive Officer - leads entire organization",
          "reportsTo": "Board of Directors",
          "organizationLevel": "Executive",
          "department": "Executive",
          "inherits": "Executive"
        },
     
      
      }
    ],
    "tenantID": "acme_corp_001",
    "tenantName": "Acme Corporation"
  };

  //    final Map<String, dynamic> departments = {
  //   "roles": [
  //     {
  //         "department_configuration": {
  //         "departmentName": "Operations13",
  //         "description": "Executive leadership and strategic direction",
  //         "departmentHeadRole": "CEO",
  //         "parentDepartment": null
  //       },
  //     },
  //   ],
  //   "tenantID": "acme_corp_001",
  //   "tenantName": "Acme Corporation"
  // };

     final Map<String, dynamic> inheritance = {
    "roles": [
      {
        "role_inheritance": {
          "parentRole": "Executive",
          "inheritsRole": "CEO"
        }
      
      }
    ],
    "tenantID": "acme_corp_001",
    "tenantName": "Acme Corporation"
  };


      var items = [
      'Engineering',
      'Sales', 
      'Finance',
    ];


  // Parse and extract roles
 List<Role> parseRolesFromJson(Map<String, dynamic> json) {
  final rolesJson = json['roles'] as List;
  return rolesJson.map((r) => Role.fromJson(r)).toList();
}
 List<Role> parseDepartmentsFromJson(Map<String, dynamic> json) {
  final rolesJson = json['roles'] as List;
  return rolesJson.map((r) => Role.fromJson(r)).toList();
}
 List<Role> parseInheritanceFromJson(Map<String, dynamic> json) {
  final rolesJson = json['roles'] as List;
  return rolesJson.map((r) => Role.fromJson(r)).toList();
}
  late AccordionController _accordionController;
  final TextEditingController _departmentNameController =
      TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String _selectedDepartmentHead = 'VP Marketing';
  String _selectedParentDepartment = 'None';
  List<Role> departmentList= [];
  List<Role> rolesList=[];
  List<Role> inheritanceList=[];
  List<String> selectedDepartments = [];


  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
      rolesList = parseRolesFromJson(data);
      // departmentList=parseDepartmentsFromJson(departments);
      inheritanceList=parseInheritanceFromJson(inheritance);
      // print('roless ${rolesList[0].roleConfiguration?.roleName ?? ""}');
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    _departmentNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Direct table view instead of accordion
            _buildDepartmentsTable(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartmentsTable(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          // Header section
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Departments',
                  style: FontManager.getCustomStyle(
                    fontSize: 16,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD1FAE5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Completed',
                        style: FontManager.getCustomStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: const Color(0xFF065F46),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      onPressed: () => _showAddDepartmentModal(context),
                      icon: const Icon(
                        Icons.add,
                        size: 14,
                        color: Colors.white,
                      ),
                      label: Text(
                        'Add Department',
                        style: FontManager.getCustomStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF007AFF),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        minimumSize: const Size(0, 28),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '25 Attributes',
                            style: FontManager.getCustomStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.keyboard_arrow_down,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Divider
          Container(
            height: 1,
            color: const Color(0xFFE5E7EB),
          ),
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFFF9FAFB),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT NAME',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    'DESCRIPTION',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'ROLES',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT HEAD ROLE',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'PARENT DEPARTMENT',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'ACTIONS',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Table rows
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: departmentList.isEmpty ? 1 : departmentList.length,
            itemBuilder: (context, index) {
              if (departmentList.isEmpty) {
                // Sample row when no data
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Executive',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 4,
                        child: Text(
                          'Executive leadership and strategic oversight',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'CEO',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'CEO',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'NA',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: const Color(0xFF9CA3AF),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {},
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFEF2F2),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Color(0xFFEF4444),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }
              
              final department = departmentList[index];
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        department.departmentConfiguration?.departmentName ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Text(
                        department.departmentConfiguration?.description ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        department.departmentConfiguration?.departmentHeadRole ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        department.departmentConfiguration?.departmentHeadRole ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        department.departmentConfiguration?.parentDepartment?.toString() ?? 'NA',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: department.departmentConfiguration?.parentDepartment != null 
                              ? Colors.black 
                              : const Color(0xFF9CA3AF),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          InkWell(
                            onTap: () => _showDeleteDepartmentConfirmation(context, index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFEF2F2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Icon(
                                Icons.delete_outline,
                                size: 16,
                                color: Color(0xFFEF4444),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          // Header section
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Departments',
                  style: FontManager.getCustomStyle(
                    fontSize: 16,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD1FAE5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Completed',
                        style: FontManager.getCustomStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: const Color(0xFF065F46),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      onPressed: () => _showAddDepartmentModal(context),
                      icon: const Icon(
                        Icons.add,
                        size: 14,
                        color: Colors.white,
                      ),
                      label: Text(
                        'Add Department',
                        style: FontManager.getCustomStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF007AFF),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        minimumSize: const Size(0, 28),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '25 Attributes',
                            style: FontManager.getCustomStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.keyboard_arrow_down,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Divider
          Container(
            height: 1,
            color: const Color(0xFFE5E7EB),
          ),
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFFF9FAFB),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT NAME',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    'DESCRIPTION',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'ROLES',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT HEAD ROLE',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'PARENT DEPARTMENT',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'ACTIONS',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Table rows
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: departmentList.isEmpty ? 1 : departmentList.length,
            itemBuilder: (context, index) {
              if (departmentList.isEmpty) {
                // Sample row when no data
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Executive',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 4,
                        child: Text(
                          'Executive leadership and strategic oversight',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'CEO',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'CEO',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'NA',
                          style: FontManager.getCustomStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: const Color(0xFF9CA3AF),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {},
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFEF2F2),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Color(0xFFEF4444),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }
              
              final department = departmentList[index];
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        department.departmentConfiguration?.departmentName ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Text(
                        department.departmentConfiguration?.description ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        department.departmentConfiguration?.departmentHeadRole ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        department.departmentConfiguration?.departmentHeadRole ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        department.departmentConfiguration?.parentDepartment?.toString() ?? 'NA',
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: department.departmentConfiguration?.parentDepartment != null 
                              ? Colors.black 
                              : const Color(0xFF9CA3AF),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          InkWell(
                            onTap: () => _showDeleteDepartmentConfirmation(context, index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFEF2F2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Icon(
                                Icons.delete_outline,
                                size: 16,
                                color: Color(0xFFEF4444),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        children: [
          // Static accordion items with sample data
           _buildSimpleAccordionItem(
            context,
            'Role Configuration',
            '12 Detected',
            'E-commerce Org',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
          ),
            _buildSimpleAccordionItem(
            context,
            'Departments',
            '6 Found',
            'Structured Hierarchy',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
          ),

          // _buildSimpleAccordionItem(
          //   context,
          //   'Role Inheritance',
          //   '3 Detected',
          //   'Permission Flow',
          //   const Color(0xFFFEE2E2),
          //   const Color(0xFF991B1B),
          // ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('simple_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Status Badge
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 150,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[ 
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildPlaceholderContent(context, title),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title) {
    switch (title) {
      case 'Departments':
        return _buildDepartmentsContent(context);
      case 'Role Configuration':
        return _buildRoleConfigurationContent(context);
      case 'Role Inheritance':
        return _buildRoleInheritanceContent(context);
      default:
        return Container();
    }
  }

  Widget _buildRoleConfigurationContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Role Configuration',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddRoleModal(context),
                icon: const Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.white,
                ),
                label: Text(
                  'Add Role',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Role Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Description',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Reports To',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          'Organization Level',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      //  Expanded(
                      //   flex: 2,
                      //   child: Text(
                      //     'Department',
                      //     style: FontManager.getCustomStyle(
                      //       fontSize: ResponsiveFontSizes.bodyMedium(context),
                      //       fontWeight: FontWeight.w600,
                      //       fontFamily: FontManager.fontFamilyTiemposText,
                      //       color: Colors.black,
                      //     ),
                      //   ),
                      // ),
                      //  Expanded(
                      //   flex: 2,
                      //   child: Text(
                      //     'inherits',
                      //     style: FontManager.getCustomStyle(
                      //       fontSize: ResponsiveFontSizes.bodyMedium(context),
                      //       fontWeight: FontWeight.w600,
                      //       fontFamily: FontManager.fontFamilyTiemposText,
                      //       color: Colors.black,
                      //     ),
                      //   ),
                      // ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: rolesList.length,
                    itemBuilder: (context,index){
                    return Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${rolesList[index].roleConfiguration?.roleName ?? ""}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          '${rolesList[index].roleConfiguration?.description ?? ""}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${rolesList[index].roleConfiguration?.reportsTo ?? ""}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                       Expanded(
                        flex: 2,
                        child: Text(
                          '${rolesList[index].roleConfiguration?.organizationLevel ?? ""}',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      //  Expanded(
                      //   flex: 2,
                      //   child: Text(
                      //     '${rolesList[index].roleConfiguration?.department ?? ""}',
                      //     style: FontManager.getCustomStyle(
                      //       fontSize: ResponsiveFontSizes.bodyMedium(context),
                      //       fontWeight: FontWeight.w400,
                      //       fontFamily: FontManager.fontFamilyTiemposText,
                      //       color: Colors.black,
                      //     ),
                      //   ),
                      // ),
                      //  Expanded(
                      //   flex: 2,
                      //   child: Text(
                      //     '${rolesList[index].roleConfiguration?.inherits ?? ""}',
                      //     style: FontManager.getCustomStyle(
                      //       fontSize: ResponsiveFontSizes.bodyMedium(context),
                      //       fontWeight: FontWeight.w400,
                      //       fontFamily: FontManager.fontFamilyTiemposText,
                      //       color: Colors.black,
                      //     ),
                      //   ),
                      // ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () => _showAddRoleModal(context, editIndex: index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.edit_outlined,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWell(
                              onTap: () => _showDeleteRoleConfirmation(context, index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.red[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                  })
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentsContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Department Configuration section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Department Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddDepartmentModal(context),
                icon: const Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.white,
                ),
                label: Text(
                  'Add Department',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Department Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Description',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Department Head Role',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Parent Department',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: ListView.builder(
                    itemCount: departmentList.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                    return  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          departmentList[index].departmentConfiguration?.departmentName ?? "",
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          departmentList[index].departmentConfiguration?.description ?? "",
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          departmentList[index].departmentConfiguration?.departmentHeadRole ?? "",
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          departmentList[index].departmentConfiguration?.parentDepartment?.toString() ?? "",
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () => _showAddDepartmentModal(context, editIndex: index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.edit_outlined,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWell(
                              onTap: () => _showDeleteDepartmentConfirmation(context, index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.red[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                  },)
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleInheritanceContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Inheritance Configuration section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Inheritance Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  _showAddInheritanceModal(context);
                },
                icon: const Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.white,
                ),
                label: Text(
                  'Add Inheritance',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF007AFF),
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Parent Role',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Inherits Role',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ), 
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: inheritanceList.length,
                    itemBuilder: (context, index) {
                    return Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          inheritanceList[index].roleInheritance?.parentRole ?? "",
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          inheritanceList[index].roleInheritance?.inheritsRole ?? "",
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {},
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.edit_outlined,
                                  size: 16,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            InkWell(
                              onTap: () => _showEditRoleModal(context),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.red[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                  },)
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }





  // add role modal popup
    void _showAddInheritanceModal(BuildContext context) {
  
    _selectedDepartmentHead = 'Engineering Manager';
    _selectedParentDepartment = 'Employee';
    List<InheritsCreateModel> inheritsModel=[];

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
            return Consumer<ObjectCreationProvider>(
              builder: (context,data,_) {
                return Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Container(
                    width: 780,
                    constraints: const BoxConstraints(maxHeight: 350),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x1A000000), // Black with 10% opacity
                          blurRadius: 16,
                          offset: Offset(0, 4),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                         Container(
                              padding: const EdgeInsets.all(24),
                              decoration: const BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      "Add Inheritance",
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                        ResponsiveFontSizes.bodyLarge(context),
                                        fontWeight: FontWeight.w600,
                                        fontFamily: FontManager.fontFamilyInter,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      // FIXED: Only fixed the functionality, kept your styling
                                      ElevatedButton(
                                        onPressed: 
                                        _selectedParentDepartment.isEmpty||_selectedDepartmentHead.isEmpty?null: () {
                
                                          inheritsModel.add(InheritsCreateModel(
                                            inheritsRole: _selectedParentDepartment,
                                             parentRole: _selectedDepartmentHead,
                                          
                                          ));
                                          buildState(() {
                                         
                                            // selectedValues will reset automatically
                                          });
                                        },
                                          
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                        _selectedParentDepartment.isEmpty||_selectedParentDepartment.isEmpty ? Colors.grey : Colors.deepPurple,
                                        ),
                                        child: Text('Add More'),
                                      ),
                                      const SizedBox(width: 8),
                                      IconButton(
                                        onPressed: () => Navigator.of(context).pop(),
                                        icon: const Icon(Icons.close),
                                        iconSize: 24,
                                        color: Colors.grey[600],
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                        Expanded(
                          child: Row(
                            children: [
                              Expanded(
                                flex: 7,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 32,right: 32,top: 16),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                   
                                  
                                      // Department Head Role and Parent Department Row
                                      Row(
                                        children: [
                                          // Department Head Role
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [

                                                    _buildEditableDropdownField(
                                                context,
                                                'Parent Role',
                                                [
                                                    'Engineering Manager', // Fixed typo only
                                                      'Engineering Director',
                                                      'CTO',
                                                      'Sales Manager',
                                                      'Sales Director',
                                                      'CEO',
                                                      'Product Director',
                                                      'HR Manager',
                                                      'HR Director',
                                                      'Board of Directors',
                                                ],
                                                _selectedDepartmentHead, (value) {
                                              buildState(() {
                                                _selectedDepartmentHead = value!;
                                                log(_selectedDepartmentHead.toString());
                                              });
                                            }
                                            ),
                                            
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          // Parent Department
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                 _buildEditableDropdownField(
                                                context,
                                                'Inherits Role',
                                                [
                                                      'Employee',
                                                      'Manager',
                                                      'Director',
                                                      'Executive',
                                                ],
                                                _selectedParentDepartment, (value) {
                                              buildState(() {
                                                _selectedParentDepartment = value!;
                                                log(_selectedParentDepartment.toString());
                                              });
                                            }
                                            ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Expanded(
                                          flex: 3,
                                          child:  Container(
                                        decoration: const BoxDecoration(
                                          border: Border(
                                            left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                          ),
                                          color: Colors.white,
                                        ),
                                        child: inheritsModel.isEmpty
                                            ? Center(child: Text("No departments added yet", style: TextStyle(color: Colors.grey)))
                                            :
                                            ListView.builder(
                                              padding: const EdgeInsets.all(16),
                                              itemCount: inheritsModel.length,
                                              itemBuilder: (context, index) {
                                                final role = inheritsModel[index];
                                                return Padding(
                                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                                  child: Row(
                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                    children: [
                                                      // Index number
                                                      Text(
                                                        '${index + 1}. ',
                                                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                                      ),
                                                  
                                                      // Role name expands to take available space
                                                      Expanded(
                                                        child: Text(
                                                          role.parentRole,
                                                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                                        ),
                                                      ),
                                                  
                                                      // Edit and Delete buttons aligned right
                                                      IconButton(
                                                        icon: const Icon(Icons.edit, color: Colors.blue),
                                                        iconSize: 20,
                                                        onPressed: () {
                                                          // Handle edit - populate fields with selected item data
                                                          final selectedInherit = inheritsModel[index];
                                                          buildState(() {
                                                            _selectedDepartmentHead = selectedInherit.parentRole;
                                                            _selectedParentDepartment = selectedInherit.inheritsRole;
                                                          });
                                                        },
                                                      ),
                                                      IconButton(
                                                        icon: const Icon(Icons.delete, color: Colors.red),
                                                        iconSize: 20,
                                                        onPressed: () {
                                                          buildState(() {
                                                            inheritsModel.removeAt(index);
                                                          });
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                            )
                                          ),
                                        )
                            ],
                          ),
                        ),
                              SizedBox(height: 10,),
                                  // Action Buttons
                                   Container(
                              padding: const EdgeInsets.all(24),
                              decoration: const BoxDecoration(
                                border: Border(
                                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                              ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        OutlinedButton(
                                          onPressed: () => Navigator.of(context).pop(),
                                          style: OutlinedButton.styleFrom(
                                            side: const BorderSide(color: Color(0xFFD1D5DB)),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(6),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 20,
                                              vertical: 10,
                                            ),
                                            backgroundColor: Colors.white,
                                          ),
                                          child: Text(
                                            'Cancel',
                                            style: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: const Color(0xFF374151),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        ElevatedButton(
                                          onPressed: () async{
                                            // Handle form submission here
                                            final provider =
                                    Provider.of<ObjectCreationProvider>(context, listen: false);
                                      // For Validate button, just close the dialog
                                       await  provider.parseValidateInheritanceEntity(inheritsModel);
                
                                      if(data.validateInheritanceModel?.inheritanceResults?[0].isValid==false){
                                        Navigator.of(context).pop();
                                           await showDialog(
                          context: this.context,
                          builder: (context) {
                return validationErrorDialog(context, "Inheritance Configuration",data.validateInheritanceModel);
                          },
                        );
                        
                                      }
                                      setState(() {
                                        
                                      });
                                   
                                            // Navigator.of(context).pop();
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(0xFF3B82F6),
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(6),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 20,
                                              vertical: 10,
                                            ),
                                          ),
                                          child: Text(
                                            'Apply This',
                                            style: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                      ],
                    ),
                  ),
                );
              }
            );
          },
        );
      },
    );
  
  }
// add role modal popup
    void _showAddDepartmentModal(BuildContext context, {int? editIndex}) {
    // Create local controllers for this modal
    final TextEditingController departmentNameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String? selectedDepartmentHead;
    String? selectedParentDepartment;
    // List<DeptCreateModel> deptCreateModel=[];
    List<String> selectedItems = [];


    // Pre-populate fields if editing
    if (editIndex != null && editIndex < departmentList.length) {
      final dept = departmentList[editIndex];
      departmentNameController.text = dept.departmentConfiguration?.departmentName ?? '';
      descriptionController.text = dept.departmentConfiguration?.description ?? '';
      selectedDepartmentHead = dept.departmentConfiguration?.departmentHeadRole ?? '';
      // Handle null parent department by setting it to "None"
      selectedParentDepartment = dept.departmentConfiguration?.parentDepartment?.toString() ?? 'None';
      
      // Debug print to see what values we're getting vs dropdown options
      log('=== EDIT DEPARTMENT DEBUG ===');
      log('Edit Department - Index: $editIndex');
      log('Department Name: ${dept.departmentConfiguration?.departmentName}');
      log('Description: ${dept.departmentConfiguration?.description}');
      log('Department Head Role FROM DB: "${dept.departmentConfiguration?.departmentHeadRole}"');
      log('Parent Department FROM DB: "${dept.departmentConfiguration?.parentDepartment}"');
      log('');
      log('Department Head Role OPTIONS: [Engineering Manager, Engineering Director, CTO, Sales Manager, Sales Director, CEO, Product Director, HR Manager, HR Director, Human Resources, Board of Directors]');
      log('Parent Department OPTIONS: [None, Engineering, Sales, Product, Executive, Technology, Finance, Human Resources]');
      log('');
      log('Selected Department Head: "$selectedDepartmentHead"');
      log('Selected Parent Department: "$selectedParentDepartment"');
      log('=== END DEBUG ===');
    }

       

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
            return Consumer<ObjectCreationProvider>(
              builder: (context,data,_) {
                return Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Container(
                    width: 780,
                    constraints: const BoxConstraints(maxHeight: 550),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x1A000000), // Black with 10% opacity
                          blurRadius: 16,
                          offset: Offset(0, 4),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                         Container(
                              padding: const EdgeInsets.all(24),
                              decoration: const BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      editIndex != null ? "Edit Department" : "Add Department",
                                      style: FontManager.getCustomStyle(
                                        fontSize:
                                        ResponsiveFontSizes.bodyLarge(context),
                                        fontWeight: FontWeight.w600,
                                        fontFamily: FontManager.fontFamilyInter,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      // Hide "Add More" button when editing
                                      if (editIndex == null) ...[
                                        ElevatedButton(
                                          onPressed: (descriptionController.text.isEmpty||departmentNameController.text.isEmpty||
                                          selectedParentDepartment == null || selectedParentDepartment!.isEmpty ||
                                          selectedDepartmentHead == null || selectedDepartmentHead!.isEmpty) ? null : () {
                                      
                                            departmentList.add(Role(
                                              roleName:  departmentNameController.text,
                                              departmentConfiguration: DepartmentConfiguration(
                                                parentDepartment: selectedParentDepartment,
                                                departmentName: departmentNameController.text, description: descriptionController.text, departmentHeadRole: selectedDepartmentHead??""),
                                              // department: selectedParentDepartment ?? '',
                                              //  deptHeadRole: selectedDepartmentHead ?? '',
                                              //  deptName: departmentNameController.text,
                                              //  description: descriptionController.text,
                                            ));
                                            buildState(() {
                                              descriptionController.clear();
                                              departmentNameController.clear();
                                              selectedDepartmentHead = null;
                                              selectedParentDepartment = null;
                                            });
                                          },
                                            
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: (descriptionController.text.isEmpty||departmentNameController.text.isEmpty||
                                          selectedParentDepartment == null || selectedParentDepartment!.isEmpty ||
                                          selectedDepartmentHead == null || selectedDepartmentHead!.isEmpty) ? Colors.grey : Colors.deepPurple,
                                          ),
                                          child: Text('Add More'),
                                        ),
                                        const SizedBox(width: 8),
                                      ],
                                      IconButton(
                                        onPressed: () => Navigator.of(context).pop(),
                                        icon: const Icon(Icons.close),
                                        iconSize: 24,
                                        color: Colors.grey[600],
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                        Expanded(
                          child: Row(
                            children: [
                              Expanded(
                                flex: 6,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 32,right: 32,top: 16),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                  
                                      // Department Name Field
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          _buildEditableFormField(
                                              context,
                                              'Department Name',
                                              departmentNameController),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                  
                                      // Description Field
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                        
                                          const SizedBox(height: 8),
                                          _buildEditableFormField(
                                              context,
                                              'Description',
                                              descriptionController),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                  
                                      // Department Head Role and Parent Department Row
                                      Row(
                                        children: [
                                          // Department Head Role
                                          
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [

                                                 _buildEditableDropdownField(
                                                context,
                                                'Department Head Role',
                                                [
                                                      'CEO', // Move CEO to first position as it's the most common
                                                      'Executive', // Add Executive to match database value
                                                      'Engineering Manager',
                                                      'Engineering Director',
                                                      'CTO',
                                                      'Sales Manager',
                                                      'Sales Director',
                                                      'Product Director',
                                                      'HR Manager',
                                                      'HR Director',
                                                      'Human Resources',
                                                      'Board of Directors',
                                                ],
                                                selectedDepartmentHead, (value) {
                                              buildState(() {
                                                selectedDepartmentHead = value;
                                                log(selectedDepartmentHead.toString());
                                              });
                                            }
                                            ),
                                              
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          // Parent Department
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                    _buildEditableDropdownField(
                                                context,
                                                'Parent Department',
                                                [
                                                      'None', // Added this to handle null values
                                                      'Engineering',
                                                      'Sales',
                                                      'Product',
                                                      'Executive',
                                                      'Technology',
                                                      'Finance',
                                                      'Human Resources',
                                                ],
                                                selectedParentDepartment, (value) {
                                              buildState(() {
                                                selectedParentDepartment = value;
                                                log(selectedParentDepartment.toString());
                                              });
                                            }
                                            ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                       const SizedBox(height: 16),
                                       
                                         Expanded(
                                           child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                             children: [
                                               Text(
                                                 'Department',
                                                 style: FontManager.getCustomStyle(
                                                   fontSize: ResponsiveFontSizes.bodyMedium(context),
                                                   fontWeight: FontWeight.w500,
                                                   fontFamily: FontManager.fontFamilyTiemposText,
                                                   color: Colors.black,
                                                 ),
                                               ),
                                               const SizedBox(height: 8),
                                               DropdownButtonHideUnderline(
                                                 child: DropdownButton2<String>(
                                                   isExpanded: true,
                                                   customButton: Container(
                                                     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                                     decoration: BoxDecoration(
                                                       border: Border.all(color: Colors.grey.shade400),
                                                       borderRadius: BorderRadius.circular(8),
                                                       color: Colors.white,
                                                     ),
                                                     child: Row(
                                                       children: [
                                                         Expanded(
                                                           child: Text(
                                                             selectedItems.isEmpty
                                                                 ? 'Select Roles'
                                                                 : selectedItems.join(', '),
                                                             style: TextStyle(
                                                               fontSize: 14,
                                                               color: selectedItems.isEmpty 
                                                                   ? Colors.grey[500] 
                                                                   : Colors.black,
                                                               overflow: TextOverflow.ellipsis,
                                                             ),
                                                             maxLines: 1,
                                                           ),
                                                         ),
                                                         const Icon(Icons.arrow_drop_down),
                                                       ],
                                                     ),
                                                   ),
                                                   value: null,
                                                   onChanged: (_) {},
                                                   items: items.map((item) {
                                                     return DropdownMenuItem<String>(
                                                       value: item,
                                                       enabled: false,
                                                       child: StatefulBuilder(
                                                         builder: (context, menuSetState) {
                                                           final isSelected = selectedItems.contains(item);
                                                           return InkWell(
                                                             onTap: () {
                                                               buildState(() {
                                                                 if (isSelected) {
                                                                   selectedItems.remove(item);
                                                                 } else {
                                                                   selectedItems.add(item);
                                                                 }
                                                               });
                                                               menuSetState(() {});
                                                             },
                                                             child: Container(
                                                               height: 44,
                                                               padding:
                                                                   const EdgeInsets.symmetric(horizontal: 12.0),
                                                               child: Row(
                                                                 children: [
                                                                   Icon(
                                                                     isSelected
                                                                         ? Icons.check_box
                                                                         : Icons.check_box_outline_blank,
                                                                     color: isSelected ? Colors.blue : Colors.grey,
                                                                   ),
                                                                   const SizedBox(width: 12),
                                                                   Expanded(
                                                                     child: Text(
                                                                       item,
                                                                       style: const TextStyle(fontSize: 14),
                                                                     ),
                                                                   ),
                                                                 ],
                                                               ),
                                                             ),
                                                           );
                                                         },
                                                       ),
                                                     );
                                                   }).toList(),
                                                   buttonStyleData: const ButtonStyleData(
                                                     padding: EdgeInsets.zero,
                                                     width: double.infinity,
                                                   ),
                                                   menuItemStyleData: const MenuItemStyleData(
                                                     padding: EdgeInsets.zero,
                                                   ),
                                                   dropdownStyleData: DropdownStyleData(
                                                     maxHeight: 200,
                                                     decoration: BoxDecoration(
                                                      //  color: Colors.white,
                                                       borderRadius: BorderRadius.all(Radius.circular(8)),
                                                       boxShadow: [
                                                         BoxShadow(
                                                           color: Color(0x1A000000),
                                                           blurRadius: 8,
                                                           offset: Offset(0, 2),
                                                         ),
                                                       ],
                                                     ),
                                                   ),
                                                 ),
                                               ),
                                              // _buildEditableDropdownField(
                                              //   context,
                                              //   'Department',
                                              //   items,
                                              //   selectedDepartments.isNotEmpty ? selectedDepartments.first : null,
                                              //   (value) {
                                              //     buildState(() {
                                              //       if (value != null) {
                                              //         selectedDepartments = [value];
                                              //       } else {
                                              //         selectedDepartments = [];
                                              //       }
                                              //     });
                                              //   }
                                              // ),
                                             ],
                                           ),
                                         ),
                                      
                                     
                                    
                                    ],
                                  ),
                                ),
                              ),
                              Expanded(
                                          flex: 3,
                                          child:  Container(
                                        decoration: const BoxDecoration(
                                          border: Border(
                                            left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                          ),
                                          color: Colors.white,
                                        ),
                                        child: departmentList.isEmpty
                                            ? Center(child: Text("No departments added yet", style: TextStyle(color: Colors.grey)))
                                            :
                                            ListView.builder(
                                              padding: const EdgeInsets.all(16),
                                              itemCount: departmentList.length,
                                              itemBuilder: (context, index) {
                                                final role = departmentList[index];
                                                return Padding(
                                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                                  child: Row(
                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                    children: [
                                                      // Index number
                                                      Text(
                                                        '${index + 1}. ',
                                                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                                      ),
                                                  
                                                      // Role name expands to take available space
                                                      Expanded(
                                                        child: Text(
                                                          role.departmentConfiguration?.departmentName??"",
                                                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                                        ),
                                                      ),
                                                  
                                                      // Edit and Delete buttons aligned right
                                                      IconButton(
                                                        icon: const Icon(Icons.edit, color: Colors.blue),
                                                        iconSize: 20,
                                                        onPressed: () {
                                                          // Handle edit - populate fields with selected item data
                                                          final selectedDept = departmentList[index];
                                                          buildState(() {
                                                            departmentNameController.text = selectedDept.departmentConfiguration?.departmentName??"";
                                                            descriptionController.text = selectedDept.departmentConfiguration?.description??"";
                                                            selectedDepartmentHead = selectedDept.departmentConfiguration?.departmentHeadRole??"";
                                                            selectedParentDepartment = selectedDept.departmentConfiguration?.parentDepartment;
                                                            departmentList.removeAt(index);
                                                          });
                                                        },
                                                      ),
                                                      IconButton(
                                                        icon: const Icon(Icons.delete, color: Colors.red),
                                                        iconSize: 20,
                                                        onPressed: () {
                                                          buildState(() {
                                                            departmentList.removeAt(index);
                                                          });
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                            )
                                          ),
                                        )
                            ],
                          ),
                        ),
                              SizedBox(height: 10,),
                                  // Action Buttons
                                   Container(
                              padding: const EdgeInsets.all(24),
                              decoration: const BoxDecoration(
                                border: Border(
                                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                              ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        OutlinedButton(
                                          onPressed: () => Navigator.of(context).pop(),
                                          style: OutlinedButton.styleFrom(
                                            side: const BorderSide(color: Color(0xFFD1D5DB)),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(6),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 20,
                                              vertical: 10,
                                            ),
                                            backgroundColor: Colors.white,
                                          ),
                                          child: Text(
                                            'Cancel',
                                            style: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: const Color(0xFF374151),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        ElevatedButton(
                                          onPressed: () async{
                                            // Handle form submission here
                                            if (editIndex != null) {
                                              // Edit mode - validate and update existing record
                                              List<DeptCreateModel> temp = [];
                                              
                                              // Add all existing departments except the one being edited
                                              for(int i=0;i<departmentList.length;i++){
                                                if(i != editIndex) {
                                                  if(i==0){
                                                    temp.add(DeptCreateModel(deptName: departmentList[i].departmentConfiguration?.departmentName ?? '', description: departmentList[i].departmentConfiguration?.description ?? '', deptHeadRole: '[None]', department: departmentList[i].departmentConfiguration?.departmentName ?? ''));
                                                  }
                                                  else{
                                                     temp.add(DeptCreateModel(deptName: departmentList[i].departmentConfiguration?.departmentName ?? '', description: departmentList[i].departmentConfiguration?.description ?? '', deptHeadRole: departmentList[i].departmentConfiguration?.departmentHeadRole ?? '', department: departmentList[i].departmentConfiguration?.parentDepartment ?? ''));
                                                  }
                                                }
                                              }
                                              
                                              // Add the edited department data
                                              temp.add(DeptCreateModel(
                                                deptName: departmentNameController.text, 
                                                description: descriptionController.text, 
                                                deptHeadRole: editIndex == 0 ? '[None]' : selectedDepartmentHead ?? '', 
                                                department: selectedParentDepartment ?? ''
                                              ));
                                              
                                              final provider = Provider.of<ObjectCreationProvider>(context, listen: false);
                                              await provider.parseValidateDeptEntity(temp);
                                        
                                              if(data.validateDepartmentModel?.isValid==false){
                                                Navigator.of(context).pop();
                                                await showDialog(context: context, builder: (context) => departmentValidationErrorDialog(
                                                  context,"Department", data.validateDepartmentModel
                                                ),);
                                              } else {
                                                if(data.saveValidDepartmentModel?.success==true){
                                                  // Update the existing record
                                                  setState(() {
                                                    departmentList[editIndex] = Role(
                                                      roleName: _departmentNameController.text, 
                                        
                                                      departmentConfiguration: DepartmentConfiguration(
                                                        departmentName: _departmentNameController.text, 
                                                        description: _descriptionController.text, 
                                                        departmentHeadRole: _selectedDepartmentHead,
                                                        parentDepartment: _selectedParentDepartment
                                                      ), 
                                                     
                                                    );
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                            } else {
                                              // Add mode - existing logic
                                              List<DeptCreateModel> temp = [];
                                             
                                              // for(int i=0;i<departmentList.length;i++){
                                              //   if(i==0){
                                              //     temp.add(DeptCreateModel(deptName: departmentList[i].departmentConfiguration?.departmentName ?? '', description: departmentList[i].departmentConfiguration?.description ?? '', deptHeadRole: '[None]', department: departmentList[i].departmentConfiguration?.departmentName ?? ''));
                                              //   }
                                              //   else{
                                              //      temp.add(DeptCreateModel(deptName: departmentList[i].departmentConfiguration?.departmentName ?? '', description: departmentList[i].departmentConfiguration?.description ?? '', deptHeadRole: departmentList[i].departmentConfiguration?.departmentHeadRole ?? '', department: departmentList[i].departmentConfiguration?.parentDepartment ?? ''));
                                              //   }
                                              // }
                                              for(int i=0;i<departmentList.length;i++){
                                                 temp.add(DeptCreateModel(deptName: departmentList[i].departmentConfiguration?.departmentName??"", description: departmentList[i].departmentConfiguration?.description??"", deptHeadRole:i==0?'[None]': departmentList[i].departmentConfiguration?.departmentHeadRole??"", department: departmentList[i].departmentConfiguration?.parentDepartment??""));
                                              }
                                                       final provider =
                                            Provider.of<ObjectCreationProvider>(context, listen: false);
                                            await  provider.parseValidateDeptEntity(temp);
                                      
                                            if(data.validateDepartmentModel?.isValid==false){
                                              Navigator.of(context).pop();
                                              await showDialog(context: context, builder: (context) => departmentValidationErrorDialog(
                                                context,"Department", data.validateDepartmentModel
                                              ),);
                                                    
                                            }else{
                                              if(data.saveValidDepartmentModel?.success==true){
                                                for(var  param in departmentList){
                                                departmentList.add(Role(roleName: param.departmentConfiguration?.departmentName??"", departmentConfiguration: DepartmentConfiguration(departmentName: param.departmentConfiguration?.departmentName??"", description: param.departmentConfiguration?.description??"", departmentHeadRole: param.departmentConfiguration?.departmentHeadRole??"",parentDepartment: param.departmentConfiguration?.departmentName??"")));
                                                }
                                                Navigator.of(context).pop();
                                                setState(() {
                                                  
                                                });
                                              }
                                            }
                                            }
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(0xFF3B82F6),
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(6),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 20,
                                              vertical: 10,
                                            ),
                                          ),
                                          child: Text(
                                            editIndex != null ? 'Update' : 'Apply This',
                                            style: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                      ],
                    ),
                  ),
                );
              }
            );
          },
        );
      },
    );
  }

// EDIT role modal popup
    void _showEditRoleModal(BuildContext context, [int? index]) {
    // Reset form fields
    _departmentNameController.text = 'Sales';
    _descriptionController.text =
        'Chief Executive Officer, overall company leadership and strategy';
    _selectedDepartmentHead = 'VP Marketing';
    _selectedParentDepartment = 'None';

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Consumer<ObjectCreationProvider>(
              builder: (context,data,_) {
                return Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Container(
                    width: 520,
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x1A000000), // Black with 10% opacity
                          blurRadius: 16,
                          offset: Offset(0, 4),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Edit Role',
                              style: FontManager.getCustomStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            InkWell(
                              onTap: () => Navigator.of(context).pop(),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: const Icon(
                                  Icons.close,
                                  size: 24,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 32),
                
                        // Department Name Field
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Role Name',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              height: 44,
                              decoration: BoxDecoration(
                                border: Border.all(color: const Color(0xFFE5E7EB)),
                                borderRadius: BorderRadius.circular(6),
                                color: Colors.white,
                              ),
                              child: TextField(
                                controller: _departmentNameController,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  //enabledBorder: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12,
                                  ),
                                  hintText: 'CEO',
                                  hintStyle: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: const Color(0xFF9CA3AF),
                                  ),
                                ),
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                
                        // Description Field
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Description',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              height: 80,
                              decoration: BoxDecoration(
                                border: Border.all(color: const Color(0xFFE5E7EB)),
                                borderRadius: BorderRadius.circular(6),
                                color: Colors.white,
                              ),
                              child: TextField(
                                controller: _descriptionController,
                                maxLines: 3,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12,
                                  ),
                                  hintText: 'Enter department description',
                                  hintStyle: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: const Color(0xFF9CA3AF),
                                  ),
                                ),
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                
                        // Department Head Role and Parent Department Row
                        Row(
                          children: [
                            // Department Head Role
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Reports To',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 44,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: const Color(0xFFE5E7EB)),
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.white,
                                    ),
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedDepartmentHead,
                                      decoration: const InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 12,
                                        ),
                                      ),
                                      style: FontManager.getCustomStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.black,
                                      ),
                                      icon: const Icon(
                                        Icons.keyboard_arrow_down,
                                        color: Color(0xFF6B7280),
                                        size: 20,
                                      ),
                                      dropdownColor: Colors.white,
                                      items: [
                                        'VP Marketing',
                                        'CEO',
                                        'CTO',
                                        'CFO',
                                        'Manager'
                                      ].map((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          _selectedDepartmentHead = newValue!;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Parent Department
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Organization Level',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 44,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: const Color(0xFFE5E7EB)),
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.white,
                                    ),
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedParentDepartment,
                                      decoration: const InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 12,
                                        ),
                                      ),
                                      style: FontManager.getCustomStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.black,
                                      ),
                                      icon: const Icon(
                                        Icons.keyboard_arrow_down,
                                        color: Color(0xFF6B7280),
                                        size: 20,
                                      ),
                                      dropdownColor: Colors.white,
                                      items: [
                                        'None',
                                        'Executive',
                                        'Sales',
                                        'Marketing',
                                        'Engineering'
                                      ].map((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          _selectedParentDepartment = newValue!;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                
                        // Department Head Role and Parent Department Row
                        Row(
                          children: [
                            // Department Head Role
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Department',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 44,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: const Color(0xFFE5E7EB)),
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.white,
                                    ),
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedDepartmentHead,
                                      decoration: const InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 12,
                                        ),
                                      ),
                                      style: FontManager.getCustomStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.black,
                                      ),
                                      icon: const Icon(
                                        Icons.keyboard_arrow_down,
                                        color: Color(0xFF6B7280),
                                        size: 20,
                                      ),
                                      dropdownColor: Colors.white,
                                      items: [
                                        'VP Marketing',
                                        'CEO',
                                        'CTO',
                                        'CFO',
                                        'Manager'
                                      ].map((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          _selectedDepartmentHead = newValue!;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Parent Department
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Inherits',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 44,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: const Color(0xFFE5E7EB)),
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.white,
                                    ),
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedParentDepartment,
                                      decoration: const InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 12,
                                        ),
                                      ),
                                      style: FontManager.getCustomStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.black,
                                      ),
                                      icon: const Icon(
                                        Icons.keyboard_arrow_down,
                                        color: Color(0xFF6B7280),
                                        size: 20,
                                      ),
                                      items: [
                                        'None',
                                        'Executive',
                                        'Sales',
                                        'Marketing',
                                        'Engineering'
                                      ].map((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          _selectedParentDepartment = newValue!;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 40),
                
                        // Action Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              height: 40,
                              child: OutlinedButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(color: Color(0xFFD1D5DB)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 10,
                                  ),
                                  backgroundColor: Colors.white,
                                ),
                                child: Text(
                                  'Cancel',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: const Color(0xFF374151),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              height: 40,
                              child: ElevatedButton(
                                onPressed: () {
                                  // Handle form submission here
                                  _handleAddDepartment();
                                  Navigator.of(context).pop();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF3B82F6),
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 10,
                                  ),
                                ),
                                child: Text(
                                  'Apply This',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              }
            );
          },
        );
      },
    );
  }


  void _showAddRoleModal(BuildContext context, {int? editIndex}) {
    // Move these OUTSIDE StatefulBuilder to maintain state - MINIMAL CHANGE
    final TextEditingController roleNameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    List<RoleCreateModel> roleList = [];
        String? selectedReportsTo;
            String? selectedDepartment;
            String? selectedOrgLevel;
            String? selectedInherits;

    // Pre-populate fields if editing
    if (editIndex != null && editIndex < rolesList.length) {
      final role = rolesList[editIndex];
      roleNameController.text = role.roleConfiguration?.roleName ?? '';
      descriptionController.text = role.roleConfiguration?.description ?? '';
      selectedReportsTo = role.roleConfiguration?.reportsTo ?? '';
      selectedDepartment = role.roleConfiguration?.department ?? '';
      selectedOrgLevel = role.roleConfiguration?.organizationLevel ?? '';
      selectedInherits = role.roleConfiguration?.inherits ?? '';
    }

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
        

            // FIXED: Only fixed the validation logic
            bool isFormValid() {
              return roleNameController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty &&
                  selectedReportsTo != null && selectedReportsTo!.isNotEmpty &&
                  selectedDepartment != null && selectedDepartment!.isNotEmpty &&
                  selectedOrgLevel != null && selectedOrgLevel!.isNotEmpty &&
                  selectedInherits != null && selectedInherits!.isNotEmpty;
            }

            return Consumer<ObjectCreationProvider>(
              builder: (context,data,_) {
                return Dialog(
                  backgroundColor: Colors.transparent,
                  child: Container(
                    width: 780,
                    constraints: const BoxConstraints(maxHeight: 500),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header - KEPT YOUR ORIGINAL DESIGN
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  editIndex != null ? "Edit Role" : "Add Role",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  // Hide "Add More" button when editing
                                  if (editIndex == null) ...[
                                    ElevatedButton(
                                      onPressed: isFormValid()
                                          ? () {
                                            log("000000000000       $selectedReportsTo");
                                        roleList.add(RoleCreateModel(
                                          roleName: roleNameController.text,
                                          description: descriptionController.text,
                                          reportsTo: selectedReportsTo ?? '',
                                          department: selectedDepartment ?? '',
                                          orgLevel: selectedOrgLevel ?? '',
                                          inherits: selectedInherits ?? '',
                                        ));
                                        buildState(() {
                                          roleNameController.clear();
                                          descriptionController.clear();
                                          selectedReportsTo = null;
                                          selectedDepartment = null;
                                          selectedOrgLevel = null;
                                          selectedInherits = null;
                                        });
                                      }
                                          : null,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: isFormValid() ? Colors.deepPurple : Colors.grey,
                                      ),
                                      child: Text('Add More'),
                                    ),
                                    const SizedBox(width: 8),
                                  ],
                                  IconButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    icon: const Icon(Icons.close),
                                    iconSize: 24,
                                    color: Colors.grey[600],
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              // Left side - Form (70% always) - KEPT YOUR ORIGINAL
                              Expanded(
                                flex: 7,
                                child: SingleChildScrollView(
                                  padding: const EdgeInsets.all(24),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Left column
                                      Expanded(
                                        child: Column(
                                          children: [
                                            _buildEditableFormField(
                                                context,
                                                'Role Name',
                                                roleNameController),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Reports To',
                                                [
                                                  'Engineering Manager', // Fixed typo only
                                                  'Engineering Director',
                                                  'CTO',
                                                  'Sales Manager',
                                                  'Sales Director',
                                                  'CEO',
                                                  'Product Director',
                                                  'HR Manager',
                                                  'HR Director',
                                                  'Board of Directors',
                                                ],
                                                selectedReportsTo, (value) {
                                              buildState(() {
                                                selectedReportsTo = value;
                                                log(selectedReportsTo.toString());
                                              });
                                            }
                                            ),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Organization Level',
                                                ['Executive',
                                                  'Director',
                                                  'Management',
                                                  'Team',
                                                  'Individual',
                                                ],
                                                selectedOrgLevel, (value) {
                                              buildState(() {
                                                selectedOrgLevel = value;
                                              });
                                            }),
                                            const SizedBox(height: 20),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 24),
                                      // Right column
                                      Expanded(
                                        child: Column(
                                          children: [
                                            _buildEditableFormField(
                                                context,
                                                'Description',
                                                descriptionController),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Department',
                                                ['Engineering',
                                                  'Sales',
                                                  'Product',
                                                  'Executive',
                                                  'Technology',
                                                  'Finance',
                                                  'Human Resources',
                                                ],
                                                selectedDepartment, (value) {
                                              buildState(() {
                                                selectedDepartment = value;
                                              });
                                            }),
                                            const SizedBox(height: 20),
                                            _buildEditableDropdownField(
                                                context,
                                                'Inherits',
                                                ['Employee',
                                                  'Manager',
                                                  'Director',
                                                  'Executive',
                                                ],
                                                selectedInherits, (value) {
                                              buildState(() {
                                                selectedInherits = value;
                                              });
                                            }),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // Right side - Always show (30% space) - KEPT YOUR ORIGINAL DESIGN
                              Expanded(
                                flex: 3,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    border: Border(
                                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                    ),
                                    color: Colors.white,
                                  ),
                                  child: roleList.isEmpty
                                      ? Center(child: Text("No roles added yet", style: TextStyle(color: Colors.grey)))
                                      :
                                  ListView.builder(
                                    padding: const EdgeInsets.all(16),
                                    itemCount: roleList.length,
                                    itemBuilder: (context, index) {
                                      final role = roleList[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8),
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            // Index number
                                            Text(
                                              '${index + 1}. ',
                                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                            ),
                
                                            // Role name expands to take available space
                                            Expanded(
                                              child: Text(
                                                role.roleName,
                                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                              ),
                                            ),
                
                                            // Edit and Delete buttons aligned right
                                            IconButton(
                                              icon: const Icon(Icons.edit, color: Colors.blue),
                                              iconSize: 20,
                                              onPressed: () {
                                                // Handle edit - populate fields with selected role data
                                                final selectedRole = roleList[index];
                                                buildState(() {
                                                  roleNameController.text = selectedRole.roleName;
                                                  descriptionController.text = selectedRole.description;
                                                  selectedReportsTo = selectedRole.reportsTo;
                                                  selectedDepartment = selectedRole.department;
                                                  selectedOrgLevel = selectedRole.orgLevel;
                                                  selectedInherits = selectedRole.inherits;
                                                  roleList.removeAt(index);
                                                });
                                              },
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.delete, color: Colors.red),
                                              iconSize: 20,
                                              onPressed: () {
                                                buildState(() {
                                                  roleList.removeAt(index);
                                                });
                                              },
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  )
                                ),
                              ),
                            ],
                          ),
                        ),
                        // KEPT YOUR ORIGINAL FOOTER DESIGN
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: const BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                onPressed: () async{
                                  final provider = Provider.of<ObjectCreationProvider>(context, listen: false);
                                  
                                  if (editIndex != null) {
                                    // Edit mode - validate single role update
                                    List<RoleCreateModel> tempRoleList = [
                                      RoleCreateModel(
                                        roleName: roleNameController.text,
                                        description: descriptionController.text,
                                        reportsTo: selectedReportsTo!,
                                        department: selectedDepartment!,
                                        orgLevel: selectedOrgLevel!,
                                        inherits: selectedInherits!,
                                      )
                                    ];
                                    
                                    await provider.parseValidateAddRoleEntity(tempRoleList);
                                    
                                    if(data.validateRoleModel?.hasErrors==true){
                                      Navigator.of(context).pop();
                                      await showDialog(context: context, builder: (context) => roleValidationErrorDialog(
                                        context,"Role Configuration", data.validateRoleModel
                                      ),);
                                    } else {
                                      // If validation passes, update the existing record
                                      setState(() {
                                        rolesList[editIndex] = Role(
                                          roleName: roleNameController.text, 
                                          roleConfiguration: RoleConfiguration(
                                            roleName: roleNameController.text, 
                                            description: descriptionController.text, 
                                            reportsTo: selectedReportsTo!, 
                                            organizationLevel: selectedOrgLevel!, 
                                            department: selectedDepartment!, 
                                            inherits: selectedInherits!
                                          ), 
                                          departmentConfiguration: DepartmentConfiguration(
                                            departmentName: selectedDepartment!, 
                                            description: descriptionController.text, 
                                            departmentHeadRole: selectedDepartment!
                                          ), 
                                          roleInheritance: RoleInheritance(
                                            parentRole: roleNameController.text, 
                                            inheritsRole: selectedInherits!
                                          )
                                        );
                                      });
                                      Navigator.of(context).pop();
                                    }
                                  } else {
                                    // Add mode - validate multiple roles
                                    await provider.parseValidateAddRoleEntity(roleList);
                                   
                                    if(data.validateRoleModel?.hasErrors==true){
                                      Navigator.of(context).pop();
                                      await showDialog(context: context, builder: (context) => roleValidationErrorDialog(
                                        context,"Role Configuration", data.validateRoleModel
                                      ),);
                                    } else {
                                      // If validation passes, add roles to the list
                                      for(var param in roleList){
                                        rolesList.add(Role(
                                          roleName: param.roleName, 
                                          roleConfiguration: RoleConfiguration(
                                            roleName: param.roleName, 
                                            description: param.description, 
                                            reportsTo: param.reportsTo, 
                                            organizationLevel: param.orgLevel, 
                                            department: param.department, 
                                            inherits: param.inherits
                                          ), 
                                          departmentConfiguration: DepartmentConfiguration(
                                            departmentName: param.department, 
                                            description: param.description, 
                                            departmentHeadRole: param.department
                                          ), 
                                          roleInheritance: RoleInheritance(
                                            parentRole: param.roleName, 
                                            inheritsRole: param.inherits
                                          )
                                        ));
                                      }
                                      Navigator.of(context).pop();
                                      setState(() {
                                        // Trigger UI update
                                      });
                                    }
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF0058FF),
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  editIndex != null ? 'Update' : 'Validate',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            );
          },
        );
      },
    );
  }
  Widget _buildEditableFormField(
      BuildContext context, String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }


  Widget _buildDropdown(String label, List<String> options, String selectedValue,
      ValueChanged<String?> onChanged) {
    return SizedBox(
      width: 260,
      child: DropdownButtonFormField<String>(
        value: selectedValue,
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(),
        ),
        items: options
            .map((value) => DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        ))
            .toList(),
        onChanged: onChanged,
      ),
    );
  }

  void _handleAddDepartment() async{
    // Handle the form submission logic here
    // RoleCreateModel? deptObject;
    log('Department Name: ${_departmentNameController.text}');
    log('Description: ${_descriptionController.text}');
    log('Department Head Role: $_selectedDepartmentHead');
    log('Parent Department: $_selectedParentDepartment');

        

    // You can add your logic here to save the department data
    // For example, add to a list, send to API, etc.
  }

  // Delete role confirmation dialog
  void _showDeleteRoleConfirmation(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Role',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "${rolesList[index].roleConfiguration?.roleName ?? ""}"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  rolesList.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Delete department confirmation dialog
  void _showDeleteDepartmentConfirmation(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Department',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "${departmentList[index].departmentConfiguration?.departmentName ?? ""}"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  departmentList.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEditableDropdownField(BuildContext context, String label,
      List<String> options, String? selectedValue, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              filled: true,
              fillColor: Colors.white,
            ),
            hint: Text(
              'Select $label',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 24,
              ),
            ),
            iconSize: 24,
            isExpanded: true,
            value: selectedValue != null && options.contains(selectedValue) ? selectedValue : null,
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    value,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget validationErrorDialog(context, title, ValidateInheritanceModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.inheritanceResults?[0].isValid !=
                                  null)
                                Text(
                                  model?.inheritanceResults?[0].validationResult?.structureErrors.toString()??"",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              // if ((provider.validateInheritanceModel?.inheritanceResults?[0].validationResult?.structureErrors??[]).isNotEmpty)
                              //   for (var element in provider
                              //       .validateInheritanceModel!
                              //       .issues!
                              //       .errors!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.validateInheritanceModel?.issues
                              //         ?.warnings !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .warnings!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.exceptions !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .exceptions!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.validationErrors !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .validationErrors!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.dependencyErrors !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .dependencyErrors!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.uniquenessIssues !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .uniquenessIssues!)
                              //     Text(
                              //       "${element.message ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.parsingIssues !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .parsingIssues!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.mongoErrors !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .mongoErrors!)
                              //     Text(
                              //       "${element["message"] ?? ''}\n",
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel?.issues
                              //         ?.postgresErrors !=
                              //     null)
                              //   for (var element in provider
                              //       .parseValidationEntityModel!
                              //       .issues!
                              //       .postgresErrors!)
                              //     Text(
                              //       '${element["message"] ?? ''}\n',
                              //       style: FontManager.getCustomStyle(
                              //         fontSize: ResponsiveFontSizes.titleSmall(
                              //             context),
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily:
                              //             FontManager.fontFamilyTiemposText,
                              //         color: Colors.black,
                              //       ),
                              //     ),
                              // if (provider.parseValidationEntityModel
                              //             ?.validationResult?.structureErrors !=
                              //         null &&
                              //     provider
                              //         .parseValidationEntityModel!
                              //         .validationResult!
                              //         .structureErrors!
                              //         .isNotEmpty)
                              //   Text(
                              //     provider.parseValidationEntityModel!
                              //         .validationResult!.structureErrors!
                              //         .join('\n'),
                              //     style: FontManager.getCustomStyle(
                              //       fontSize:
                              //           ResponsiveFontSizes.titleSmall(context),
                              //       fontWeight: FontWeight.w400,
                              //       fontFamily:
                              //           FontManager.fontFamilyTiemposText,
                              //       color: Colors.black,
                              //     ),
                              //   ),
                              // if (provider
                              //             .parseValidationEntityModel
                              //             ?.validationResult
                              //             ?.requiredFieldErrors !=
                              //         null &&
                              //     provider
                              //         .parseValidationEntityModel!
                              //         .validationResult!
                              //         .requiredFieldErrors!
                              //         .isNotEmpty)
                              //   Text(
                              //     provider.parseValidationEntityModel!
                              //         .validationResult!.requiredFieldErrors!
                              //         .join('\n'),
                              //     style: FontManager.getCustomStyle(
                              //       fontSize:
                              //           ResponsiveFontSizes.titleSmall(context),
                              //       fontWeight: FontWeight.w400,
                              //       fontFamily:
                              //           FontManager.fontFamilyTiemposText,
                              //       color: Colors.black,
                              //     ),
                              //   ),
                              // if (provider.parseValidationEntityModel
                              //             ?.validationResult?.dataTypeErrors !=
                              //         null &&
                              //     provider
                              //         .parseValidationEntityModel!
                              //         .validationResult!
                              //         .dataTypeErrors!
                              //         .isNotEmpty)
                              //   Text(
                              //     provider.parseValidationEntityModel!
                              //         .validationResult!.dataTypeErrors!
                              //         .join('\n'),
                              //     style: FontManager.getCustomStyle(
                              //       fontSize:
                              //           ResponsiveFontSizes.titleSmall(context),
                              //       fontWeight: FontWeight.w400,
                              //       fontFamily:
                              //           FontManager.fontFamilyTiemposText,
                              //       color: Colors.black,
                              //     ),
                              //   ),
                              // if (provider.parseValidationEntityModel
                              //             ?.validationResult?.customErrors !=
                              //         null &&
                              //     provider
                              //         .parseValidationEntityModel!
                              //         .validationResult!
                              //         .customErrors!
                              //         .isNotEmpty)
                              //   Text(
                              //     provider.parseValidationEntityModel!
                              //         .validationResult!.customErrors!
                              //         .join('\n'),
                              //     style: FontManager.getCustomStyle(
                              //       fontSize:
                              //           ResponsiveFontSizes.titleSmall(context),
                              //       fontWeight: FontWeight.w400,
                              //       fontFamily:
                              //           FontManager.fontFamilyTiemposText,
                              //       color: Colors.black,
                              //     ),
                              //   ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }






     Widget departmentValidationErrorDialog(context, title, ValidateDepartmentModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.success !=
                                  null)
                                Text(
                                  model?.failureReason??"",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if ((model?.dependencyErrors??[]).isNotEmpty)
                                for (var element in model
                                    !.dependencyErrors??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.warnings !=
                                  null)
                                for (var element in model?.issues?.warnings??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.exceptions !=
                                  null)
                                for (var element in model?.issues?.exceptions??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.validationErrors !=
                                  null)
                                for (var element in model?.issues?.validationErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.dependencyErrors !=
                                  null)
                                for (DependencyError element in model?.issues?.dependencyErrors??[])
                                  Text(
                                    "${element.message?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.uniquenessIssues !=
                                  null)
                                for (var element in model?.issues?.uniquenessIssues??[])
                                  Text(
                                    "${element['message'] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.parsingIssues !=
                                  null)
                                for (var element in model?.issues?.parsingIssues??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.mongoErrors !=
                                  null)
                                for (var element in model?.issues?.mongoErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.postgresErrors !=
                                  null)
                                for (var element in model?.issues?.postgresErrors??[])
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model
                                          ?.validationResult?.structureErrors !=
                                      null &&
                                 (model?.validationResult?.structureErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.structureErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.dataTypeErrors !=
                                      null &&
                                  (model?.validationResult?.dataTypeErrors??[]).isNotEmpty)
                                Text(
                                 (model?.validationResult?.dataTypeErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult?.customErrors !=
                                      null &&
                                  (model?.validationResult?.customErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.customErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }


   Widget roleValidationErrorDialog(context, title, ValidateAddRoleModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.success !=
                                  null)
                                Text(
                                  model?.failureReason??"",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if ((model?.dependencyErrors??[]).isNotEmpty)
                                for (var element in model
                                    !.dependencyErrors??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.warnings !=
                                  null)
                                for (var element in model?.issues?.warnings??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.exceptions !=
                                  null)
                                for (var element in model?.issues?.exceptions??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.validationErrors !=
                                  null)
                                for (var element in model?.issues?.validationErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.dependencyErrors !=
                                  null)
                                for (RoleDependencyError element in model?.issues?.dependencyErrors??[])
                                  Text(
                                    "${element.message?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.uniquenessIssues !=
                                  null)
                                for (var element in model?.issues?.uniquenessIssues??[])
                                  Text(
                                    "${element['message'] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.parsingIssues !=
                                  null)
                                for (var element in model?.issues?.parsingIssues??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.mongoErrors !=
                                  null)
                                for (var element in model?.issues?.mongoErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.postgresErrors !=
                                  null)
                                for (var element in model?.issues?.postgresErrors??[])
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model
                                          ?.validationResult?.structureErrors !=
                                      null &&
                                 (model?.validationResult?.structureErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.structureErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.dataTypeErrors !=
                                      null &&
                                  (model?.validationResult?.dataTypeErrors??[]).isNotEmpty)
                                Text(
                                 (model?.validationResult?.dataTypeErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult?.customErrors !=
                                      null &&
                                  (model?.validationResult?.customErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.customErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  }

// Shared HoverBellIcon component
class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;
  OverlayEntry? _overlayEntry;
  final GlobalKey _bellIconKey = GlobalKey();
  bool _isOverPopup = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final RenderBox? renderBox =
        _bellIconKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 280,
        top: position.dy + size.height + 8,
        child: MouseRegion(
          onEnter: (_) {
            _isOverPopup = true;
          },
          onExit: (_) {
            _isOverPopup = false;
            Future.delayed(Duration(milliseconds: 50), () {
              if (mounted && !isHovered && !_isOverPopup) {
                _removeOverlay();
              }
            });
          },
          child: Material(
            color: Colors.transparent,
            child: _buildHoverPopup(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleMouseExit() {
    setState(() => isHovered = false);
    Future.delayed(Duration(milliseconds: 50), () {
      if (mounted && !isHovered && !_isOverPopup) {
        _removeOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => isHovered = true);
        _showOverlay();
      },
      onExit: (_) {
        _handleMouseExit();
      },
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Icon(
              key: _bellIconKey,
              Icons.notifications_outlined,
              size: 18,
              color: Color(0xffFF2019),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHoverPopup() {
    return Container(
      width: 300,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'This Objects is already exists in your library. You need to rename the Objects to proceed.',
            textAlign: TextAlign.center,
            style: FontManager.getCustomStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              height: 1.4,
            ),
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400, width: 1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    backgroundColor: Colors.white,
                  ),
                  child: Text(
                    'Continue',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.black87,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xff007AFF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    elevation: 0,
                  ),
                  child: Text(
                    'Resolve',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.white,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }


    
}


class User {
  final String name;
  final int id;

  User({required this.name, required this.id});

  @override
  String toString() {
    return 'User(name: $name, id: $id)';
  }
}
