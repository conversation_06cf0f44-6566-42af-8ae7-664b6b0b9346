import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';

import '../../../../../models/object_creation_model.dart';
import '../../../../../models/customer_model.dart';
import '../constants/customer_onboarding_constants.dart';
import 'form_table_widget.dart';

/// Reusable Accordion Item Widget with Status Support
class AccordionItemWidget extends StatelessWidget {
  final String title;
  final String? status; // Made optional for auto-determination
  final String? count; // Made optional for auto-determination
  final Color? backgroundColor; // Made optional for auto-determination
  final Color? textColor; // Made optional for auto-determination
  final bool showAttributeTable;
  final AccordionController accordionController;
  final ObjectCreationModel? objectData;
  final List<BusinessRule1>? businessRulesData;
  final Function(String, BusinessRule1)? onUpdateRule;
  final bool isExpandEnabled; // Control expansion capability
  final bool isSaved; // Track save state
  final int? itemCount; // For auto status determination
  final Widget? customContent; // Allow custom content

  const AccordionItemWidget({
    super.key,
    required this.title,
    this.status,
    this.count,
    this.backgroundColor,
    this.textColor,
    required this.showAttributeTable,
    required this.accordionController,
    this.objectData,
    this.businessRulesData,
    this.onUpdateRule,
    this.isExpandEnabled = true,
    this.isSaved = false,
    this.itemCount,
    this.customContent,
  });

  /// Helper method to get status text based on count
  String _getStatusFromCount(int count) {
    if (count == 0) {
      return 'Missing';
    } else if (count > 0 && count < 5) {
      return 'Partial Completion';
    } else {
      return 'Completed';
    }
  }

  /// Helper method to get background color based on count
  Color _getBackgroundColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFFFEE2E2); // Red background for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFFFEF3C7); // Yellow background for partial
    } else {
      return const Color(0xFFD1FAE5); // Green background for completed
    }
  }

  /// Helper method to get text color based on count
  Color _getTextColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFF991B1B); // Red text for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFF92400E); // Yellow text for partial
    } else {
      return const Color(0xFF065F46); // Green text for completed
    }
  }

  /// Get status information based on title or count
  Map<String, dynamic> _getStatusInfo() {
    // If explicit values are provided, use them
    if (status != null &&
        count != null &&
        backgroundColor != null &&
        textColor != null) {
      return {
        'status': status!,
        'count': count!,
        'backgroundColor': backgroundColor!,
        'textColor': textColor!,
      };
    }

    // Auto-determine based on itemCount if provided
    if (itemCount != null) {
      return {
        'status': _getStatusFromCount(itemCount!),
        'count': _getCountText(itemCount!),
        'backgroundColor': _getBackgroundColorFromCount(itemCount!),
        'textColor': _getTextColorFromCount(itemCount!),
      };
    }

    // Default fallback based on title
    return _getDefaultStatusInfo();
  }

  /// Get count text based on title and count
  String _getCountText(int count) {
    switch (title) {
      case 'Core Metadata':
      case 'Object Details':
        return count == 0 ? '0 Entity Detected' : '$count Entity Detected';
      case 'Process Ownership':
        return count == 0 ? '0 roles' : '$count roles';
      case 'Trigger Definition':
        return count == 0 ? '0 triggers' : '$count triggers';
      case 'Local Objectives':
        return count == 0 ? '0 objectives' : '$count objectives';
      case 'Pathways Definitions':
        return count == 0 ? '0 pathways' : '$count pathways';
      case 'Pathways Detail':
        return count == 0 ? '0 routes' : '$count routes';
      case 'Business Rules':
        return count == 0 ? '0 rules' : '$count rules';
      case 'Validation Rules':
        return count == 0 ? '0 validations' : '$count validations';
      case 'Attributes Details':
        return count == 0 ? '0 Attributes' : '$count Attributes';
      default:
        return count == 0 ? '0 Configure' : '$count Configure';
    }
  }

  /// Get default status info based on title
  Map<String, dynamic> _getDefaultStatusInfo() {
    switch (title) {
      case 'Core Metadata':
      case 'Object Details':
        return {
          'status': 'Partial Completion',
          'count': '3 Entity Detected',
          'backgroundColor': const Color(0xFFFEF3C7),
          'textColor': const Color(0xFF92400E),
        };
      case 'Process Ownership':
        return {
          'status': 'Completed',
          'count': '3 roles',
          'backgroundColor': const Color(0xFFD1FAE5),
          'textColor': const Color(0xFF065F46),
        };
      case 'Trigger Definition':
        return {
          'status': 'Completed',
          'count': '1 triggers',
          'backgroundColor': const Color(0xFFD1FAE5),
          'textColor': const Color(0xFF065F46),
        };
      case 'Local Objectives':
        return {
          'status': 'Missing',
          'count': '3 objectives',
          'backgroundColor': const Color(0xFFFEE2E2),
          'textColor': const Color(0xFF991B1B),
        };
      case 'Pathways Definitions':
        return {
          'status': 'Completed',
          'count': '2 pathways',
          'backgroundColor': const Color(0xFFD1FAE5),
          'textColor': const Color(0xFF065F46),
        };
      case 'Pathways Detail':
        return {
          'status': 'Not Configured',
          'count': '2 routes',
          'backgroundColor': const Color(0xFFF3F4F6),
          'textColor': const Color(0xFF6B7280),
        };
      case 'Business Rules':
        return {
          'status': 'Completed',
          'count': '1 rule',
          'backgroundColor': const Color(0xFFD1FAE5),
          'textColor': const Color(0xFF065F46),
        };
      case 'Validation Rules':
        return {
          'status': 'Not Configured',
          'count': '1 validation',
          'backgroundColor': const Color(0xFFF3F4F6),
          'textColor': const Color(0xFF6B7280),
        };
      case 'Attributes Details':
        return {
          'status': 'Completed',
          'count': '15 Attributes',
          'backgroundColor': const Color(0xFFD1FAE5),
          'textColor': const Color(0xFF065F46),
        };
      default:
        return {
          'status': 'Missing',
          'count': '0 Configure',
          'backgroundColor': const Color(0xFFFEE2E2),
          'textColor': const Color(0xFF991B1B),
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    final isExpanded = accordionController.isPanelExpanded('simple_$title');
    final statusInfo = _getStatusInfo();
    final displayStatus = statusInfo['status'] as String;
    final displayCount = statusInfo['count'] as String;
    final displayBackgroundColor = statusInfo['backgroundColor'] as Color;
    final displayTextColor = statusInfo['textColor'] as Color;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: isExpandEnabled
                ? () => accordionController.togglePanel('simple_$title')
                : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Dynamic width
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w500
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Status Badge - Dynamic width
                        if (displayStatus.isNotEmpty)
                          Flexible(
                            flex: 2,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: displayBackgroundColor,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                displayStatus,
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.labelSmall(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: displayTextColor,
                                ),
                                overflow: TextOverflow.visible,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      displayCount,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded && isExpandEnabled)
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: customContent ?? _getContentForTitle(context),
            ),
        ],
      ),
    );
  }

  Widget _getContentForTitle(BuildContext context) {
    switch (title) {
      case 'Core Metadata':
        return FormTableWidget(
          businessRulesData: businessRulesData ??
              CustomerOnboardingConstants.defaultBusinessRules,
          onUpdateRule: onUpdateRule,
          title: 'Business Rules Configuration',
          hideAddButton: true,
          hideDeleteButton: true,
          disableFieldAndSourceInEdit: true,
        );
      case 'Process Ownership':
        return FormTableWidget(
          title: 'Role Assignment',
          genericData: _getProcessOwnershipData(),
          headers: const ['Role Type', 'Assigned Role', 'Source'],
          columnWidths: const [150, 200, 120],
          addButtonText: 'Add Role',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      case 'Trigger Definition':
        return FormTableWidget(
          title: 'Trigger Configuration',
          genericData: _getTriggerDefinitionData(),
          headers: const [
            'Trigger Type',
            'Trigger Attributes',
            'Trigger Condition',
            'Trigger Schedule'
          ],
          columnWidths: const [120, 200, 250, 120],
          addButtonText: 'Add Trigger Definition',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      case 'Local Objectives':
        return FormTableWidget(
          title: 'Objective Management',
          genericData: _getLocalObjectivesData(),
          headers: const ['LO ID', 'Objective Name', 'Agent Type'],
          columnWidths: const [80, 200, 120],
          addButtonText: 'Add Objective',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      case 'Pathways Definitions':
        return FormTableWidget(
          title: 'Pathway Definitions Configuration',
          genericData: _getPathwaysDefinitionsData(),
          headers: const [
            'Pathway ID',
            'Pathway Name',
            'Description',
            'Status'
          ],
          columnWidths: const [100, 150, 250, 100],
          addButtonText: 'Add Pathway Definition',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      case 'Pathways Detail':
        return FormTableWidget(
          title: 'Pathway Routing Configuration',
          genericData: _getPathwaysDetailData(),
          headers: const [
            'Objective',
            'Agent Type',
            'Route Type',
            'Routing Conditions'
          ],
          columnWidths: const [150, 120, 120, 250],
          addButtonText: 'Add Pathway',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      case 'Business Rules':
        return FormTableWidget(
          title: 'Business Rules Configuration',
          genericData: _getBusinessRulesData(),
          headers: const [
            'Role Type',
            'Entity',
            'Description',
            'Inputs',
            'Operation',
            'Output',
            'Validation'
          ],
          columnWidths: const [100, 100, 200, 150, 120, 150, 120],
          addButtonText: 'Add Rule',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      case 'Validation Rules':
        return FormTableWidget(
          title: 'Validation Rules Configuration',
          genericData: _getValidationRulesData(),
          headers: const [
            'Rule Name',
            'Description',
            'Inputs',
            'Operation',
            'Output',
            'Validation Type',
            'Error Message'
          ],
          columnWidths: const [120, 180, 150, 120, 150, 120, 180],
          addButtonText: 'Add Validation',
          showAddButton: true,
          enableActions: true,
          maxHeight: 300,
        );
      default:
        return _buildPlaceholderContent(context);
    }
  }

  /// Get Process Ownership data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getProcessOwnershipData() {
    return [
      {
        'roletype': 'Originator',
        'assignedrole': 'Customer',
        'source': 'Inferred',
      },
      {
        'roletype': 'Executive',
        'assignedrole': 'Customer',
        'source': 'Inferred',
      },
      {
        'roletype': 'Business Sponsor',
        'assignedrole': 'Business Executive',
        'source': 'Inferred',
      },
    ];
  }

  /// Get Trigger Definition data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getTriggerDefinitionData() {
    return [
      {
        'triggertype': 'user-initiated',
        'triggerattributes': 'Customer.name, Customer.email',
        'triggercondition': 'User initiates the onboarding process',
        'triggerschedule': 'on-demand',
      },
    ];
  }

  /// Get Local Objectives data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getLocalObjectivesData() {
    return [
      {
        'loid': 'LO-1',
        'objectivename': 'ValidateRegistration',
        'agenttype': 'SYSTEM',
      },
      {
        'loid': 'LO-2',
        'objectivename': 'ValidateRegistration',
        'agenttype': 'SYSTEM',
      },
      {
        'loid': 'LO-3',
        'objectivename': 'ValidateRegistration',
        'agenttype': 'SYSTEM',
      },
    ];
  }

  /// Get Pathways Definitions data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getPathwaysDefinitionsData() {
    return [
      {
        'pathwayid': 'PW-001',
        'pathwayname': 'Registration Pathway',
        'description': 'Standard customer registration flow',
        'status': 'Active',
      },
      {
        'pathwayid': 'PW-002',
        'pathwayname': 'Validation Pathway',
        'description': 'Customer validation and verification flow',
        'status': 'Active',
      },
    ];
  }

  /// Get Pathways Detail data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getPathwaysDetailData() {
    return [
      {
        'objective': 'ValidateRegistration',
        'agenttype': 'Human',
        'routetype': 'Sequential',
        'routingconditions':
            'If validation passes, route to SendEmailVerification',
      },
      {
        'objective': 'ValidateRegistration',
        'agenttype': 'Machine',
        'routetype': 'Sequential',
        'routingconditions':
            'If validation passes, route to SendEmailVerification',
      },
    ];
  }

  /// Get Business Rules data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getBusinessRulesData() {
    return [
      {
        'roletype': 'Executive',
        'entity': 'Customer',
        'description': 'Ensure email is unique across all customers',
        'inputs': 'Customer.email, Customer.registration_date',
        'operation': 'conditional_logic',
        'output': 'Customer with email_valid',
        'validation': 'PRE_INSERT',
      },
    ];
  }

  /// Get Validation Rules data structure based on the screenshot requirements
  List<Map<String, dynamic>> _getValidationRulesData() {
    return [
      {
        'rulename': 'go_completeness_check',
        'description':
            'Validate GO has all required components before deployment',
        'inputs': 'GO with components',
        'operation': 'validation_function',
        'output': 'ValidationResult with status, message',
        'validationtype': 'PRE_DEPLOY',
        'errormessage': 'GO is missing required components',
      },
    ];
  }

  Widget _buildAttributeConfigurationContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Attribute Configuration',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.headlineSmall(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          SizedBox(height: AppSpacing.md),
          _buildAttributeTable(context),
        ],
      ),
    );
  }

  Widget _buildAttributeTable(BuildContext context) {
    final sampleAttributes = [
      {'name': 'Customer ID', 'type': 'String', 'required': 'Yes'},
      {'name': 'Full Name', 'type': 'String', 'required': 'Yes'},
      {'name': 'Email', 'type': 'Email', 'required': 'Yes'},
      {'name': 'Phone', 'type': 'Phone', 'required': 'No'},
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(AppSpacing.sm),
            decoration: const BoxDecoration(
              color: Color(0xFFF9FAFB),
              borderRadius: BorderRadius.vertical(top: Radius.circular(4)),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Attribute Name',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Type',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Required',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Rows
          ...sampleAttributes.asMap().entries.map((entry) {
            final index = entry.key;
            final attr = entry.value;
            return Container(
              padding: EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: index < sampleAttributes.length - 1
                        ? const Color(0xFFE5E7EB)
                        : Colors.transparent,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      attr['name']!,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      attr['type']!,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      attr['required']!,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: attr['required'] == 'Yes'
                            ? Colors.red
                            : Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.lg),
      child: Center(
        child: Text(
          'Content for "$title" will be implemented here.',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600]!,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
