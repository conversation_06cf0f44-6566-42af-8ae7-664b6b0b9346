// To parse this JSON data, do
//
//     final getRolesListModel = getRolesListModelFromJson(jsonString);

import 'dart:convert';

GetRolesListModel getRolesListModelFromJson(String str) => GetRolesListModel.fromJson(json.decode(str));

String getRolesListModelToJson(GetRolesListModel data) => json.encode(data.toJson());

class GetRolesListModel {
    bool? success;
    String? message;
    RoleData? data;
    dynamic error;
    DateTime? timestamp;

    GetRolesListModel({
        this.success,
        this.message,
        this.data,
        this.error,
        this.timestamp,
    });

    factory GetRolesListModel.fromJson(Map<String, dynamic> json) => GetRolesListModel(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null ? null : RoleData.fromJson(json["data"]),
        error: json["error"],
        timestamp: json["timestamp"] == null ? null : DateTime.parse(json["timestamp"]),
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data?.toJson(),
        "error": error,
        "timestamp": timestamp?.toIso8601String(),
    };
}

class RoleData {
    String? tenantId;
    String? tableName;
    List<PostgresDatum>? postgresData;
    List<dynamic>? mongoData;
    Summary? summary;
    DateTime? retrievedAt;

    RoleData({
        this.tenantId,
        this.tableName,
        this.postgresData,
        this.mongoData,
        this.summary,
        this.retrievedAt,
    });

    factory RoleData.fromJson(Map<String, dynamic> json) => RoleData(
        tenantId: json["tenant_id"],
        tableName: json["table_name"],
        postgresData: json["postgres_data"] == null ? [] : List<PostgresDatum>.from(json["postgres_data"]!.map((x) => PostgresDatum.fromJson(x))),
        mongoData: json["mongo_data"] == null ? [] : List<dynamic>.from(json["mongo_data"]!.map((x) => x)),
        summary: json["summary"] == null ? null : Summary.fromJson(json["summary"]),
        retrievedAt: json["retrieved_at"] == null ? null : DateTime.parse(json["retrieved_at"]),
    );

    Map<String, dynamic> toJson() => {
        "tenant_id": tenantId,
        "table_name": tableName,
        "postgres_data": postgresData == null ? [] : List<dynamic>.from(postgresData!.map((x) => x.toJson())),
        "mongo_data": mongoData == null ? [] : List<dynamic>.from(mongoData!.map((x) => x)),
        "summary": summary?.toJson(),
        "retrieved_at": retrievedAt?.toIso8601String(),
    };
}

class PostgresDatum {
    String? roleId;
    String? name;
    String? tenantId;
    dynamic inheritsFrom;
    DateTime? createdAt;
    DateTime? updatedAt;
    String? description;
    dynamic permissions;
    dynamic scope;
    dynamic classification;
    dynamic specialConditions;
    String? versionType;
    int? id;
    dynamic reportsToRoleId;
    String? organizationalLevel;
    dynamic departmentId;
    dynamic naturalLanguage;
    int? version;
    String? createdBy;
    String? updatedBy;

    PostgresDatum({
        this.roleId,
        this.name,
        this.tenantId,
        this.inheritsFrom,
        this.createdAt,
        this.updatedAt,
        this.description,
        this.permissions,
        this.scope,
        this.classification,
        this.specialConditions,
        this.versionType,
        this.id,
        this.reportsToRoleId,
        this.organizationalLevel,
        this.departmentId,
        this.naturalLanguage,
        this.version,
        this.createdBy,
        this.updatedBy,
    });

    factory PostgresDatum.fromJson(Map<String, dynamic> json) => PostgresDatum(
        roleId: json["role_id"],
        name: json["name"],
        tenantId: json["tenant_id"],
        inheritsFrom: json["inherits_from"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        description: json["description"],
        permissions: json["permissions"],
        scope: json["scope"],
        classification: json["classification"],
        specialConditions: json["special_conditions"],
        versionType: json["version_type"],
        id: json["id"],
        reportsToRoleId: json["reports_to_role_id"],
        organizationalLevel: json["organizational_level"],
        departmentId: json["department_id"],
        naturalLanguage: json["natural_language"],
        version: json["version"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
    );

    Map<String, dynamic> toJson() => {
        "role_id": roleId,
        "name": name,
        "tenant_id": tenantId,
        "inherits_from": inheritsFrom,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "description": description,
        "permissions": permissions,
        "scope": scope,
        "classification": classification,
        "special_conditions": specialConditions,
        "version_type": versionType,
        "id": id,
        "reports_to_role_id": reportsToRoleId,
        "organizational_level": organizationalLevel,
        "department_id": departmentId,
        "natural_language": naturalLanguage,
        "version": version,
        "created_by": createdBy,
        "updated_by": updatedBy,
    };
}

class Summary {
    int? postgresCount;
    int? mongoCount;
    int? totalCount;

    Summary({
        this.postgresCount,
        this.mongoCount,
        this.totalCount,
    });

    factory Summary.fromJson(Map<String, dynamic> json) => Summary(
        postgresCount: json["postgres_count"],
        mongoCount: json["mongo_count"],
        totalCount: json["total_count"],
    );

    Map<String, dynamic> toJson() => {
        "postgres_count": postgresCount,
        "mongo_count": mongoCount,
        "total_count": totalCount,
    };
}
