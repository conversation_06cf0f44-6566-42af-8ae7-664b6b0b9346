class Role {
  final String roleName;
  final RoleConfiguration? roleConfiguration;
  final DepartmentConfiguration? departmentConfiguration;
  final RoleInheritance? roleInheritance;

  Role({
    required this.roleName,
    this.roleConfiguration,
    this.departmentConfiguration,
    this.roleInheritance,
  });

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        roleName: json['roleName'] ?? '',
        roleConfiguration: json['role_configuration'] != null 
            ? RoleConfiguration.fromJson(json['role_configuration']) 
            : null,
        departmentConfiguration: json['department_configuration'] != null 
            ? DepartmentConfiguration.fromJson(json['department_configuration']) 
            : null,
        roleInheritance: json['role_inheritance'] != null 
            ? RoleInheritance.fromJson(json['role_inheritance']) 
            : null,
      );

  Map<String, dynamic> toJson() => {
        'roleName': roleName,
        if (roleConfiguration != null) 'role_configuration': roleConfiguration!.toJson(),
        if (departmentConfiguration != null) 'department_configuration': departmentConfiguration!.toJson(),
        if (roleInheritance != null) 'role_inheritance': roleInheritance!.toJson(),
      };
}

class RoleConfiguration {
  final String roleName;
  final String description;
  final String reportsTo;
  final String organizationLevel;
  final String department;
  final String inherits;

  RoleConfiguration({
    required this.roleName,
    required this.description,
    required this.reportsTo,
    required this.organizationLevel,
    required this.department,
    required this.inherits,
  });

  factory RoleConfiguration.fromJson(Map<String, dynamic> json) => RoleConfiguration(
        roleName: json['roleName'] ?? '',
        description: json['description'] ?? '',
        reportsTo: json['reportsTo'] ?? '',
        organizationLevel: json['organizationLevel'] ?? '',
        department: json['department'] ?? '',
        inherits: json['inherits'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'roleName': roleName,
        'description': description,
        'reportsTo': reportsTo,
        'organizationLevel': organizationLevel,
        'department': department,
        'inherits': inherits,
      };
}

class DepartmentConfiguration {
  final String departmentName;
  final String description;
  final String departmentHeadRole;
  final String? parentDepartment;
  final String? role;

  DepartmentConfiguration({
    required this.departmentName,
    required this.description,
    required this.departmentHeadRole,
    this.parentDepartment,
    this.role
  });

  factory DepartmentConfiguration.fromJson(Map<String, dynamic> json) =>
      DepartmentConfiguration(
        departmentName: json['departmentName'] ?? '',
        description: json['description'] ?? '',
        departmentHeadRole: json['departmentHeadRole'] ?? '',
        parentDepartment: json['parentDepartment'],
        role: json['role']
      );

  Map<String, dynamic> toJson() => {
        'departmentName': departmentName,
        'description': description,
        'departmentHeadRole': departmentHeadRole,
        'parentDepartment': parentDepartment,
        'role':role
      };
}

class RoleInheritance {
  final String parentRole;
  final String inheritsRole;

  RoleInheritance({
    required this.parentRole,
    required this.inheritsRole,
  });

  factory RoleInheritance.fromJson(Map<String, dynamic> json) => RoleInheritance(
        parentRole: json['parentRole'] ?? '',
        inheritsRole: json['inheritsRole'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'parentRole': parentRole,
        'inheritsRole': inheritsRole,
      };
}
