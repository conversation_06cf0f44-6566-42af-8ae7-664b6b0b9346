import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/responsive_home_new_builder.dart';

class CreateObjectScreenMobile extends StatefulWidget {
  const CreateObjectScreenMobile({super.key});

  @override
  State<CreateObjectScreenMobile> createState() =>
      _CreateObjectScreenMobileState();
}

class _CreateObjectScreenMobileState extends State<CreateObjectScreenMobile> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isLoading = false;
  bool isShowingSpinner = false;

  @override
  void initState() {
    super.initState();
    chatController = TextEditingController();
    _multimediaService = MultimediaService();
  }

  @override
  void dispose() {
    chatController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (chatController.text.trim().isNotEmpty) {
      // Handle message sending logic here
      Logger.info('Sending message: ${chatController.text}');

      // Show spinner for 2 seconds
      setState(() {
        isShowingSpinner = true;
      });

      // Clear the chat field
      chatController.clear();

      // After 2 seconds, navigate to extract details screen with animation
      Future.delayed(Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            isShowingSpinner = false;
          });

          // Navigate with smooth right-to-left animation
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  ResponsiveHomeNewBuilder(
                      routeName: ScreenConstants.extractDetailsObjectMobile),
              transitionDuration: Duration(milliseconds: 500),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(1.0, 0.0), // Start from right
                    end: Offset.zero, // End at center
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  )),
                  child: child,
                );
              },
            ),
          );
        }
      });
    }
  }

  void _cancelRequest() {
    // Handle cancel request logic here
    setState(() {
      isLoading = false;
    });
  }

  void _handleFileSelection(String fileName, String filePath) {
    // Handle file selection logic here
    Logger.info('File selected: $fileName at $filePath');
  }

  void _toggleRecording() {
    // Handle recording toggle logic here
    Logger.info('Toggle recording');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      drawer: const CustomDrawer(),
      //backgroundColor: Color(0xfff6f6f6),
      body: Column(
        children: [
          // Main content area
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xffffffff),
              ),
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Spacer to push content down
                  const Spacer(),

                  // Text above chat field
                  Padding(
                    padding: const EdgeInsets.only(bottom: 40),
                    child: Text(
                      'I will help you to create your entity. Please tell me what entity you want to create?',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: "TiemposText",
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),

                  // Small spinner above chat field when processing
                  if (isShowingSpinner)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFF0058FF)),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Processing...',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              fontFamily: "TiemposText",
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Chat field at the bottom
                  ChatField(
                    isGeneralLoading: isLoading,
                    isFileLoading: false,
                    isSpeechLoading: false,
                    onSendMessage: _sendMessage,
                    onCancelRequest: _cancelRequest,
                    onFileSelected: _handleFileSelection,
                    onToggleRecording: _toggleRecording,
                    controller: chatController,
                    multimediaService: _multimediaService,
                    height: 56,
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
