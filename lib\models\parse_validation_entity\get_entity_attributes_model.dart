// To parse this JSON data, do
//
//     final getEntityAttributesModel = getEntityAttributesModelFromJson(jsonString);

import 'dart:convert';

GetEntityAttributesModel getEntityAttributesModelFromJson(String str) =>
    GetEntityAttributesModel.fromJson(json.decode(str));

String getEntityAttributesModelToJson(GetEntityAttributesModel data) =>
    json.encode(data.toJson());

class GetEntityAttributesModel {
  bool? success;
  String? entityId;
  List<MongoDraftAttribute>? postgresAttributes;
  List<MongoDraftAttribute>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;
  String? operation;

  GetEntityAttributesModel({
    this.success,
    this.entityId,
    this.postgresAttributes,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
    this.operation,
  });

  GetEntityAttributesModel copyWith({
    bool? success,
    String? entityId,
    List<MongoDraftAttribute>? postgresAttributes,
    List<MongoDraftAttribute>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
    String? operation,
  }) =>
      GetEntityAttributesModel(
        success: success ?? this.success,
        entityId: entityId ?? this.entityId,
        postgresAttributes: postgresAttributes ?? this.postgresAttributes,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
        operation: operation ?? this.operation,
      );

  factory GetEntityAttributesModel.fromJson(Map<String, dynamic> json) =>
      GetEntityAttributesModel(
        success: json["success"],
        entityId: json["entity_id"],
        postgresAttributes: json["postgres_attributes"] == null
            ? []
            : List<MongoDraftAttribute>.from(
                json["postgres_attributes"]!.map((x) => x)),
        mongoDrafts: json["mongo_drafts"] == null
            ? []
            : List<MongoDraftAttribute>.from(json["mongo_drafts"]!
                .map((x) => MongoDraftAttribute.fromJson(x))),
        totalPostgres: json["total_postgres"],
        totalDrafts: json["total_drafts"],
        operation: json["operation"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "entity_id": entityId,
        "postgres_attributes": postgresAttributes == null
            ? []
            : List<dynamic>.from(postgresAttributes!.map((x) => x)),
        "mongo_drafts": mongoDrafts == null
            ? []
            : List<dynamic>.from(mongoDrafts!.map((x) => x.toJson())),
        "total_postgres": totalPostgres,
        "total_drafts": totalDrafts,
        "operation": operation,
      };
}

class MongoDraftAttribute {
  String? id;
  String? attributeId;
  String? entityId;
  String? tenantId;
  String? name;
  String? displayName;
  String? datatype;
  bool? isPrimaryKey;
  bool? isForeignKey;
  bool? isRequired;
  bool? isUnique;
  String? defaultType;
  String? defaultValue;
  String? description;
  String? helperText;
  bool? isCalculated;
  String? calculationFormula;
  int? version;
  String? status;
  String? naturalLanguage;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;

  MongoDraftAttribute({
    this.id,
    this.attributeId,
    this.entityId,
    this.tenantId,
    this.name,
    this.displayName,
    this.datatype,
    this.isPrimaryKey,
    this.isForeignKey,
    this.isRequired,
    this.isUnique,
    this.defaultType,
    this.defaultValue,
    this.description,
    this.helperText,
    this.isCalculated,
    this.calculationFormula,
    this.version,
    this.status,
    this.naturalLanguage,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  MongoDraftAttribute copyWith({
    String? id,
    String? attributeId,
    String? entityId,
    String? tenantId,
    String? name,
    String? displayName,
    String? datatype,
    bool? isPrimaryKey,
    bool? isForeignKey,
    bool? isRequired,
    bool? isUnique,
    String? defaultType,
    String? defaultValue,
    String? description,
    String? helperText,
    bool? isCalculated,
    String? calculationFormula,
    int? version,
    String? status,
    String? naturalLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) =>
      MongoDraftAttribute(
        id: id ?? this.id,
        attributeId: attributeId ?? this.attributeId,
        entityId: entityId ?? this.entityId,
        tenantId: tenantId ?? this.tenantId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        datatype: datatype ?? this.datatype,
        isPrimaryKey: isPrimaryKey ?? this.isPrimaryKey,
        isForeignKey: isForeignKey ?? this.isForeignKey,
        isRequired: isRequired ?? this.isRequired,
        isUnique: isUnique ?? this.isUnique,
        defaultType: defaultType ?? this.defaultType,
        defaultValue: defaultValue ?? this.defaultValue,
        description: description ?? this.description,
        helperText: helperText ?? this.helperText,
        isCalculated: isCalculated ?? this.isCalculated,
        calculationFormula: calculationFormula ?? this.calculationFormula,
        version: version ?? this.version,
        status: status ?? this.status,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory MongoDraftAttribute.fromJson(Map<String, dynamic> json) =>
      MongoDraftAttribute(
        id: json["_id"],
        attributeId: json["attribute_id"],
        entityId: json["entity_id"],
        tenantId: json["tenant_id"],
        name: json["name"],
        displayName: json["display_name"],
        datatype: json["datatype"],
        isPrimaryKey: json["is_primary_key"],
        isForeignKey: json["is_foreign_key"],
        isRequired: json["is_required"],
        isUnique: json["is_unique"],
        defaultType: json["default_type"],
        defaultValue: json["default_value"],
        description: json["description"],
        helperText: json["helper_text"],
        isCalculated: json["is_calculated"],
        calculationFormula: json["calculation_formula"],
        version: json["version"],
        status: json["status"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "attribute_id": attributeId,
        "entity_id": entityId,
        "tenant_id": tenantId,
        "name": name,
        "display_name": displayName,
        "datatype": datatype,
        "is_primary_key": isPrimaryKey,
        "is_foreign_key": isForeignKey,
        "is_required": isRequired,
        "is_unique": isUnique,
        "default_type": defaultType,
        "default_value": defaultValue,
        "description": description,
        "helper_text": helperText,
        "is_calculated": isCalculated,
        "calculation_formula": calculationFormula,
        "version": version,
        "status": status,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
      };
}
