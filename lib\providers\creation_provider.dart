import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/get_all_my_library_model.dart';

class CreationProvider extends ChangeNotifier {
  int _currentMiddleScreen = 2;
  // 0 - roles 1- objects 2-GO
  bool _isGoMyLibraryClicked = false;
  bool _isLeftPanelVisible = true; // Control left panel visibility

  // Role auto-population data
  RolesPostgre? _prePopulatedRole;
  List<RolesPostgre> _allRoles = [];

  int get currentMiddleScreen => _currentMiddleScreen;
  bool get isGoMyLibraryClicked => _isGoMyLibraryClicked;
  bool get isLeftPanelVisible => _isLeftPanelVisible;
  RolesPostgre? get prePopulatedRole => _prePopulatedRole;
  List<RolesPostgre> get allRoles => _allRoles;

  set currentMiddleScreen(value) {
    _currentMiddleScreen = value;
    notifyListeners();
  }

  set isGoMyLibraryClicked(value) {
    _isGoMyLibraryClicked = value;
    notifyListeners();
  }

  set isLeftPanelVisible(value) {
    _isLeftPanelVisible = value;
    notifyListeners();
  }

  /// Show the left panel and switch to the specified tab
  void showLeftPanelWithTab(int tabIndex) {
    _isLeftPanelVisible = true;
    _currentMiddleScreen = tabIndex;
    notifyListeners();
  }

  /// Toggle the left panel visibility
  void toggleLeftPanel() {
    _isLeftPanelVisible = !_isLeftPanelVisible;
    notifyListeners();
  }

  /// Set role data for auto-population in role creation screen
  void setRoleForCreation(RolesPostgre? role, List<RolesPostgre> allRoles) {
    _prePopulatedRole = role;
    _allRoles = allRoles;
    notifyListeners();
  }

  /// Clear role data
  void clearRoleData() {
    _prePopulatedRole = null;
    _allRoles = [];
    notifyListeners();
  }
}
